import { ref, useContext } from '@nuxtjs/composition-api';
import { camelCaseKeys, snakeCaseKeys } from '~/util/functions';
import { whitelistInitState } from '~/store/whitelist';

export default function useWhiteList() {
  const { $axios, store } = useContext();
  const fetchWhiteList = async (params) => {
    try {
      const { data } = await $axios.get('/whitelist', {
        params: snakeCaseKeys(params),
      });
      await store.dispatch('whitelist/setWhiteLists', camelCaseKeys(data));
    } catch (e) {
      await store.dispatch('whitelist/setWhiteLists', camelCaseKeys(whitelistInitState));
    }

  };

  const createWhiteList = (data) => {
    return $axios.post('/whitelist', snakeCaseKeys(data));
  };

  const importExcelSerial = async (fileExcel, campId) => {
    try {
      console.log(`${fileExcel} - ${campId}`);
      const formData = new FormData();
      formData.append('file', fileExcel);
      const headers = { 'Content-Type': 'multipart/form-data' };
      return await $axios.post(`/files/import-white-list?campaign_id=${campId}`, formData, { headers });
    } catch (e) {
      console.log(e);
    }
  }
  const importExcelOps = async (fileExcel, campId) => {
    try {
      console.log(`${fileExcel} - ${campId}`);
      const formData = new FormData();
      formData.append('file', fileExcel);
      const headers = { 'Content-Type': 'multipart/form-data' };
      return await $axios.post(`/whitelist/import?campaign_id=${campId}`, formData, { headers });
    } catch (e) {
      console.log(e);
    }
  }


  const changeStatus = async (row, status) => {
    if (![1, 2].includes(status)) return;
    const id = row.id;
    const entity = {
      status: status
    }
    try {
      const { success } = await $axios.post(`/serial/toggle-status/${id}`, entity);
      return !!success;
    } catch (e) {
      return false
    }
  }

  return {
    fetchWhiteList,
    createWhiteList,
    importExcelSerial,
    importExcelOps,
    changeStatus,
  };
}

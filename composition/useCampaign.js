import {useContext, ref} from '@nuxtjs/composition-api'
import {snakeCaseKeys} from '@/util/functions.js';
import {reverse} from 'lodash';
const FormData = require('form-data');


export default function useCampaign() {
  const {$axios} = useContext();
  const listCampaign = ref([])
  const campaign = ref(null)
  const isSuccess = ref(false)
  const fetchCampaignList = async (params = {}) => {
    params.all = true;
    const {success, data} = await $axios.get('/campaign', {
      params: snakeCaseKeys(params),
    });
    if (success) {
      listCampaign.value = reverse(data);
    }
  };
  const changeStatus = async (row, status) => {
    if (![1, 2].includes(status)) return;
    const id = row.id;
    const entity = {
      status: status,
      gift_detail_id: row.gift_detail_id,
      campaign_id: row.campaign_id,
      category_id: row.category_id,
      model: row.model,
      valuex: row.valuex,
      title: row.title
    }
    await _updateCampaign(entity, id);
  }

  const saveCampaign = async (entity, id = 0) => {
    entity.start_time = new Date(entity.start_time).getTime() / 1000
    entity.end_time = new Date(entity.end_time).getTime() / 1000
    entity.end_time_for_admin = new Date(entity.end_time_for_admin).getTime() / 1000
    await _updateCampaign(entity, id);
  }
  const _updateCampaign = async (entity, id = 0) => {
    const formData = new FormData()
    Object.keys(entity).forEach((key) => {
      if (entity[key] instanceof File) {
        formData.append(key, entity[key]); // Append file
      } else if (typeof entity[key] === "object") {
        formData.append(key, JSON.stringify(entity[key])); // Chuyển object thành JSON string
      } else {
        formData.append(key, String(entity[key])); // Convert dữ liệu khác về string
      }
    });
    const {success} = await $axios.put(`/campaign/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    if (success) {
      isSuccess.value = true;
    }
  }

  const createCampaign = async (entities) => {
    entities.start_time = new Date(entities.start_time).getTime() / 1000
    entities.end_time = new Date(entities.end_time).getTime() / 1000
    entities.end_time_for_admin = new Date(entities.end_time_for_admin).getTime() / 1000
    const {success} = await $axios.post(`/campaign`, entities);
    if (success) {
      isSuccess.value = true;
    }
  }

  const fetchCampaignDetail = async (id) => {
    const {success, data} = await $axios.get(`/campaign/${id}`);
    if (success) {
      campaign.value = data;
    }
  }


  return {
    fetchCampaignList,
    listCampaign,
    changeStatus,
    isSuccess,
    fetchCampaignDetail,
    campaign,
    saveCampaign,
    createCampaign
  };
}

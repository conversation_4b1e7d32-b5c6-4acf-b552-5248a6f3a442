import {useContext} from '@nuxtjs/composition-api';
import {camelCaseKeys, snakeCaseKeys} from '~/util/functions';
import {imageInitState} from '~/store/image';
import httpStatus from 'http-status';

export default function useImages() {
  const {$axios, store} = useContext();

  const fetchImages = async (params) => {
    try {
      const {data} = await $axios.get('/files/upload-image', {
        params: snakeCaseKeys(params),
      });
      store.dispatch('image/setImages', camelCaseKeys(data));
    } catch (e) {
      if (e.data.error.code === httpStatus.NOT_FOUND) {
        store.dispatch('image/setImages', camelCaseKeys(imageInitState));
      }
    }
  };

  const createImage = (file) => {
    try {
      return $axios.post(`/files/upload-image/${file.name}`, file, {
        headers: {
          'Content-Type': file.type,
        }
      });
    }catch (e) {
      console.log(e)
    }
  }
  return {
    fetchImages,
    createImage,
  };
}

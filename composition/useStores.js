import { useContext } from '@nuxtjs/composition-api';
import { camelCaseKeys, snakeCaseKeys } from '~/util/functions';

export default function useStores() {
  const { store, $axios } = useContext();

  const fetchStores = async (params) => {
    try {
      const { data } = await $axios.get('/stores', {
        params: snakeCaseKeys(params),
      });
      store.dispatch('store/setStores', camelCaseKeys(data));
    } catch (e) {
      return {
        items: [],
      };
    }
  };

  return {
    fetchStores,
  };
}

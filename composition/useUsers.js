import { useContext } from '@nuxtjs/composition-api'
import { camelCaseKeys, snakeCaseKeys } from '~/util/functions'

export default function useUsers() {
    const { $axios, store } = useContext()

    // L<PERSON><PERSON> sách user
    const fetchUsers = async (params = {}) => {
        const res = await $axios.get('/users', {
            params: snakeCaseKeys(params)
        })
        return camelCaseKeys(res)
    }

    // Lấy chi tiết user
    const fetchUserDetail = async (userId) => {
        const { data } = await $axios.get(`/users/${userId}`)
        return camelCaseKeys(data)
    }

    // Tạo user mới
    const createUser = async (userData) => {
        const { data } = await $axios.post('/users', snakeCaseKeys(userData))
        return camelCaseKeys(data)
    }

    // Cập nhật thông tin user
    const updateUser = async (userId, userData) => {
        const { data } = await $axios.put(`/users/${userId}`, snakeCaseKeys(userData))
        return camelCaseKeys(data)
    }

    // Cập nhật role cho user
    const updateUserRole = async (userId, roleId) => {
        const { data } = await $axios.put(`/users/${userId}/role`, snakeCaseKeys({ roleId }))
        return camelCaseKeys(data)
    }

    // Xóa user
    const deleteUser = async (userId) => {
        const { data } = await $axios.delete(`/users/${userId}`)
        return camelCaseKeys(data)
    }

    // Lấy danh sách roles
    const fetchRoles = async () => {
        const { data } = await $axios.get('/roles')
        return camelCaseKeys(data)
    }

    // Lấy danh sách permissions
    const fetchPermissions = async () => {
        const { data } = await $axios.get('/permissions')
        return camelCaseKeys(data)
    }

    // Lấy danh sách campaigns (nếu cần)
    const fetchCampaigns = async () => {
        const { data } = await $axios.get('/campaigns')
        return camelCaseKeys(data)
    }

    // Thay đổi mật khẩu user
    const changePassword = async (userId, passwordData) => {
        const { data } = await $axios.put(`/users/${userId}/password`, snakeCaseKeys(passwordData))
        return camelCaseKeys(data)
    }

    return {
        fetchUsers,
        fetchUserDetail,
        createUser,
        updateUser,
        updateUserRole,
        deleteUser,
        fetchRoles,
        fetchPermissions,
        fetchCampaigns,
        changePassword
    }
} 
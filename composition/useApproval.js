import { useContext } from '@nuxtjs/composition-api';
import { snakeCaseKeys } from '~/util/functions';

export default function useApproval() {
  const { store, $axios } = useContext();

  function transformData(claim, approval, claimDetail, note, sms_refuse) {
    return snakeCaseKeys({
      claimId: claim.id,
      approveId: store.$auth.user.id,
      approveStatus: approval.status,
      approveNote: approval.note,
      claimDetail: Object.keys(claimDetail).map((key) => claimDetail[key]),
      note: note,
      sms_refuse: sms_refuse,
    });
  }

  function transformDataSaveNote(claim, note, account, type) {
    return snakeCaseKeys({
      claimId: claim.id,
      note: note,
      account: account,
      type: type
    });
  }

  function doSaveNote(claim, note, account, type) {
    const transformedData = transformDataSaveNote(claim, note, account, type);

    return $axios({
      url: '/approve/note',
      method: 'post',
      data: transformedData,
    });
  }

  const doFirstApproval = (claim, approval, claimDetail, note) => {
    const transformedData = transformData(claim, approval, claimDetail, note);

    // if (approval.status === 1) {
    //   transformedData.note = undefined;
    // }

    return $axios({
      url: '/approve/one',
      method: 'post',
      data: transformedData,
    });
  };

  const doSecondApproval = (claim, approval, claimDetail, note, sms_refuse) => {
    const transformedData = transformData(claim, approval, claimDetail, note, sms_refuse);

    // if (approval.status === 1) {
    //   transformedData.note = undefined;
    // }
    return $axios({
      url: '/approve/two',
      method: 'post',
      data: transformedData,
    });
  };

  const fetchNoteLog = async (claimId) => {
    const response = await $axios.get('/approve/note', {
      params: {claim_id: claimId}
    });
    return response;
  };

  return {
    doFirstApproval,
    doSecondApproval,
    doSaveNote,
    fetchNoteLog,
  };
}

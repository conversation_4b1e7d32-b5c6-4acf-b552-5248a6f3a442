import { useContext } from '@nuxtjs/composition-api';
import { camelCaseKeys, snakeCaseKeys, convertClaimInfo } from '~/util/functions';
import { claimInitState } from '~/store/claim';
import httpStatus from 'http-status';
import _ from "lodash";

export default function useClaims() {
  const { $axios, store } = useContext();

  const fetchClaims = async (params) => {
    try {
      const { data } = await $axios.get('/claims', {
        params: snakeCaseKeys(params),
      });
      store.dispatch('claim/setClaims', camelCaseKeys(data));
    } catch (e) {
      if (e.data.error.code === httpStatus.NOT_FOUND) {
        store.dispatch('claim/setClaims', camelCaseKeys(claimInitState));
      }
    }
  };

  const createClaim = (data) => {
    return $axios.post('/claims/admin', snakeCaseKeys(data));
  };

  const createClaimV2 = (data) => {
    // return $axios.post('/claims?v2=1', snakeCaseKeys(data));
    return $axios.post('/claims/admin', snakeCaseKeys(data));
  };

  const fetchDetailClaim = async (claimId) => {
    try {
      const { data } = await $axios.get(`claims/${claimId}`, { params: {} });
      const transformedData = convertClaimInfo(camelCaseKeys(data));
      store.dispatch('claim/setDetailClaim', transformedData);
      return transformedData;
    } catch (e) {
      return null;
    }
  };

  const resentGift = async (claimId) => {
    const { data } = await $axios.post('approve/resend_gift', snakeCaseKeys({ claimId }));
    return data;
  };

  const cloneClaim = async (claimId) => {
    const { data } = await $axios.post(`claim/clone/${claimId}`);
    return data;
  };

  const updateActiveKplus = async (claimId, body) => {
    const { data } = await $axios.put(`claims/${claimId}?v2=1`, snakeCaseKeys(body));
    return data;
  };

  function saveCustomerInfo(customerId, data) {
    console.log(data);
    return $axios({
      url: `/customers/${customerId}`,
      method: 'put',
      data: snakeCaseKeys(data),
    });
  }

  function saveCustomerTempInfo(customerId, data) {
    return $axios({
      url: `/customers/temp/${customerId}`,
      method: 'put',
      data: snakeCaseKeys(data),
    });
  }
  function updateOcrData(params) {
    const {claim_id,field, value, admin_id, note} = snakeCaseKeys(params);
    return $axios({
      url: `/ocr/claim/${claim_id}/update-manual`,
      method: 'put',
      data: {
        value: value,
        field: _.snakeCase(field),
        admin_id: admin_id,
        note: note
      }
    });
  }

  function updateOcrStickerData(params) {
    const {claim_id, field, ref_id, value, admin_id, note} = snakeCaseKeys(params);
    return $axios({
      url: `/ocr/claim/${claim_id}/update-sticker-manual`,
      method: 'put',
      data: {
        ref_id: ref_id,
        value: value,
        field: _.snakeCase(field),
        admin_id: admin_id,
        note: note
      }
    });
  }

  const importExcelApprove = async (fileExcel, campId, approveTime, approveId) => {
    try{
      console.log(`${fileExcel} - ${campId}`);
      const formData = new FormData();
      formData.append('file', fileExcel);
      const headers = { 'Content-Type': 'multipart/form-data' };
      console.log(formData);
      return await $axios.post(`/approve/import-approve-file?campaign_id=${campId}&approve_id=${approveId}&approve_time=${approveTime}`, formData, {headers});
    } catch (e) {
      console.log(e);
    }
  }

  const importExcelAdminCreateClaim = async (fileExcel) => {
    try {
      console.log(fileExcel);
      const formData = new FormData();
      formData.append('file', fileExcel);
      const headers = { 'Content-Type': 'multipart/form-data' };
      console.log(formData);
      return await $axios.post(`/claims/import-claim`, formData, { headers });
    } catch (e) {
      console.log(e);
    }
  }

  const updateWarrantyInfo = async (params) => {
    try {

      return await $axios.put(`/ocr/claim/${params.id}/update-warranty-manual`,params);
    } catch (e) {
      return false;
    }
  }

  return {
    fetchClaims,
    fetchDetailClaim,
    resentGift,
    createClaim,
    updateActiveKplus,
    createClaimV2,
    saveCustomerInfo,
    importExcelApprove,
    importExcelAdminCreateClaim,
    updateOcrData,
    updateOcrStickerData,
    cloneClaim,
    updateWarrantyInfo,
    saveCustomerTempInfo
  };
}

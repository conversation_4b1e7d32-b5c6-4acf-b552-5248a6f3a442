import {useContext, ref} from '@nuxtjs/composition-api'
import {snakeCaseKeys} from '../util/functions.js';

export default function useCategory() {
  const category = ref([])
  const fetchCategory = async (params = {}) => {

  category.value = [
    {
      id: 1,
      title: "Quà vật lý LG"
    },
    {
      id: 2,
      title: "Quà voucher Urbox"
    },
    {
      id: 3,
      title: "Topup LYCHEE"
    },
    {
      id: 4,
      title: "Quà K+"
    },
    {
      id: 5,
      title: "Quà ghép combo"
    }
  ]
  };

  return {
    fetchCategory,
    category,

  };
}

import { useContext } from '@nuxtjs/composition-api';
import { camelCaseKeys, snakeCaseKeys, convertClaimInfo } from '~/util/functions';
import { claimTempInitState } from '~/store/claimTemp';
import httpStatus from 'http-status';

export default function useClaimsTemp() {
  const { $axios, store } = useContext();

  const fetchClaims = async (params) => {
    try {
      const { data } = await $axios.get('temp/claims', {
        params: snakeCaseKeys(params),
      });
      store.dispatch('claimTemp/setClaims', camelCaseKeys(data));
    } catch (e) {
      if (e.data.error.code === httpStatus.NOT_FOUND) {
        store.dispatch('claimTemp/setClaims', camelCaseKeys(claimTempInitState));
      }
    }
  };

  const fetchDetailClaim = async (claimId) => {
    try {
      const { data } = await $axios.get(`temp/claim/${claimId}`, { params: {} });
      const transformedData = convertClaimInfo(camelCaseKeys(data));
      store.dispatch('claim/setDetailClaim', transformedData);
      return transformedData;
    } catch (e) {
      return null;
    }
  };



  return {
    fetchClaims,
    fetchDetailClaim,
  };
}

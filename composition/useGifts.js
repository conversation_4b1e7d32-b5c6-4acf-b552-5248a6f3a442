import {useContext, ref} from '@nuxtjs/composition-api'
import {snakeCaseKeys} from '../util/functions.js';
import {useCategory} from "./index";

export default function useGifts() {
  const {store, $axios} = useContext();
  const listGifts = ref([])
  const giftDetail = ref(null)
  const isSuccess = ref(false)
  const fetchGiftList = async (params = {}) => {
    params.all = true;
    const {success, data} = await $axios.get('/gifts/list', {
      params: snakeCaseKeys(params),
    });
    store.dispatch('gift/setGifts', {data});
    if (success) {
      const {fetchCategory, category} = useCategory();
      await fetchCategory();
      data.map(item => {
        const selectedCate = category.value.find((cate => cate.id === item.category_id))
        item.category = {
          id: 0,
          title: ''
        }
        if (selectedCate) {
          item.category = selectedCate
        }
        return item
      })
      listGifts.value = data;
    }
  };
  const changeStatus = async (row, status) => {
    if (![1, 2].includes(status)) return;
    const id = row.id;
    const entity = {
      status: status,
      gift_detail_id: row.gift_detail_id,
      campaign_id: row.campaign_id,
      category_id: row.category_id,
      model: row.model,
      valuex: row.valuex,
      title: row.title
    }
    await _updateGift(entity, id);
  }

  const saveGift = async (entity, id = 0) => {
    await _updateGift(entity, id);
  }
  const _updateGift = async (entity, id = 0) => {
    const {success} = await $axios.put(`/gift/${id}`, entity);
    if (success) {
      isSuccess.value = true;
    }
  }

  const createGift = async (entities) => {
    const data = entities.map(item => {
      return {
        campaign_id: item[0],
        gift_detail_id: item[1],
        title: item[2],
        model: item[3],
        category_id: item[4],
        valuex: item[5],
        related_model: item[6],
      }
    })
    const {success} = await $axios.post(`/gifts/list/bulk`, {data: data});
    if (success) {
      isSuccess.value = true;
    }
  }

  const fetchGiftDetail = async (id) => {
    const {success, data} = await $axios.get(`/gift/${id}`);
    if (success) {
      giftDetail.value = data;
    }
  }


  return {
    fetchGiftList,
    listGifts,
    changeStatus,
    isSuccess,
    fetchGiftDetail,
    giftDetail,
    saveGift,
    createGift
  };
}

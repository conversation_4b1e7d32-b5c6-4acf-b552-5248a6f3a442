import { useContext } from '@nuxtjs/composition-api';
import { clientApi } from '../util/client-api.js';

export default function useImage() {
  const { $config } = useContext();

  const uploadImage = async (file) => {
    const url = $config.imageUrlUpload.replace('{fileName}', file.name);
    return clientApi.post(url, file, {
      headers: {
        'Content-Type': file.type,
      },
    });
  };

  return {
    uploadImage,
  };
}

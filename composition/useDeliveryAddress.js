import {useContext} from '@nuxtjs/composition-api';
import {camelCaseKeys, snakeCaseKeys} from '~/util/functions';

export default function fetchDeliveryAddress() {
  const { $axios, store } = useContext();

  const fetchDeliveryAddress = async (claimId) => {
    try {
      const { data } = await $axios.get(`delivery_info?claim_id=${claimId}`, { params: {} });
      return camelCaseKeys(data);
    } catch (e) {
      return null;
    }
  };

  const fetchProvince = async () => {
    try {
      const { data } = await $axios.get('location/provinces', { params: {} });
      return data;
    } catch (e) {
      return null;
    }
  };

  const fetchDistrict = async (provinceId) => {
    try {
      const { data } = await $axios.get(`location/districts?city_id=${provinceId}`, { params: {} });
      return data;
    } catch (e) {
      return null;
    }
  };

  const fetchWard = async (provinceId,districtId) => {
    try {
      const { data } = await $axios.get(`location/wards?province_id=${provinceId}&district_id=${districtId}`, { params: {} });
      return data;
    } catch (e) {
      return null;
    }
  };

  const doSaveDeliveryAddress = async (param) => {
    try {
      const { data } = await $axios.put(`delivery_info`, snakeCaseKeys(param));
      return data;
    } catch (e) {
      return null;
    }
  };

  


  return {
    fetchDeliveryAddress,
    fetchProvince,
    fetchDistrict,
    fetchWard,
    doSaveDeliveryAddress
  };
}

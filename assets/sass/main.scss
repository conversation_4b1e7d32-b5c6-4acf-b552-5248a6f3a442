.auth-layout {
  .header-login {
    background-image: url('https://demos.creative-tim.com/material-kit/assets/img/bg2.jpg');
    &:after {
      background: rgba(101, 47, 142, 0.64);
      background: linear-gradient(87deg, $orange 0, adjust-hue($orange, 25%) 100%);
      background: -webkit-linear-gradient(85deg, $orange, rgba(125, 46, 185, 0.45));
      position: absolute;
      z-index: 1;
      width: 100%;
      height: 100%;
      display: block;
      left: 0;
      top: 0;
      content: '';
    }
    .notifications,
    .header-body {
      z-index: 2;
      position: relative;
    }
  }

  .login-container {
    z-index: 2;
    position: relative;
  }
}

label {
  line-height: 16px;
  font-size: 14px;
}

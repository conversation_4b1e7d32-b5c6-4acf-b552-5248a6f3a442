<template>
  <div
    v-if="isLoading && isAuthenticated"
    class="d-flex w-100 h-100 justify-content-center align-items-center position-fixed"
    style="z-index: 9999; background-color: rgba(255, 255, 255, 0.7)"
  >
    <div class="d-block">
      <div class="d-flex justify-content-center">
        <i class="d-block fas fa-circle-notch fa-spin fa-4x"></i>
      </div>
      <h3 class="mt-3" style="color: #525f7f">{{ 'Đang tải dữ liệu...' }}</h3>
    </div>
  </div>
</template>

<script>
import {computed, defineComponent, useContext, useStore} from '@nuxtjs/composition-api';

  export default defineComponent({
    name: 'LoadingContent',
    setup() {
      // const store = useStore();
      const { store } = useContext();
      const isLoading = computed(() => {
        return store.getters['getIsLoading'];
      });

      const isAuthenticated = computed(() => {
        return store.getters['isAuthenticated']
      })

      return {
        isLoading,
        isAuthenticated
      };
    },
  });
</script>

<style scoped></style>

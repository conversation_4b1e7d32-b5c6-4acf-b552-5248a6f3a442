<template>
  <el-tooltip :content="tooltip_text" placement="top">
    <div
        class="toggle"
        :class=[this.state_class]
        @click.self="onClick">
      <div
          class="draggable"
          @mousedown.prevent="dragStart"
          :style="style">
      </div>
    </div>
  </el-tooltip>
</template>

<script>
export default {
  name: "ToggleStatus",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    valueX: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      width: 100,
      state: false,
      pressed: 0,
      position: 0,
      statusTextX: ""
    }
  },
  mounted() {
    this.toggle(this.value)
  },
  computed: {
    style() {
      return {
        transform: `translateX(${this.pos_percentage})`
      }
    },
    tooltip_text(){
      if(this.state === true) return `Bấm để ẩn quà`
      return `Bấm để hiển thị quà`
    },
    pos_percentage() {
      return `${this.position / this.width * 120}%`
    },
    state_class() {
      if (this.state) {
        return 'active'
      }
    }
  },
  watch: {
    position() {
      this.state = this.position >= 50
    },
    value(){
      //this.state = this.value
      this.toggle(this.value)
    }
  },
  methods: {
    onClick() {
      this.toggle(!this.state)
      this.emit()
    },
    toggle(state) {
      this.state = state
      this.position = !state
          ? 0
          : 100
    },
    dragging(e) {
      const pos = e.clientX - this.$el.offsetLeft
      const percent = pos / this.width * 100
      this.position = percent <= 0
          ? 0
          : percent >= 100
              ? 100
              : percent
    },
    dragStart(e) {
      this.startTimer()
      window.addEventListener('mousemove', this.dragging)
      window.addEventListener('mouseup', this.dragStop)
    },
    dragStop() {
      window.removeEventListener('mousemove', this.dragging)
      window.removeEventListener('mouseup', this.dragStop)
      this.resolvePosition()
      clearInterval(this.$options.interval)
      if (this.pressed < 30) {
        this.toggle(!this.state)
      }
      this.pressed = 0
      this.emit()
    },
    startTimer() {
      this.$options.interval = setInterval(() => {
        this.pressed++
      }, 1)
    },
    resolvePosition() {
      this.position = this.state
          ? 100
          : 0
    },
    emit() {
      this.$emit('input', this.state, this.valueX)
    }
  }
}
</script>

<style scoped lang="scss">
$width: 40px;
$height: 20px;
$background: #ff0000;
$background-active: #2dce89;
$border-color: transparent;
$button-size: 16px;
$button-color: #FFF;

.toggle {
  width: $width;
  height: $height;
  background: $background;
  border-radius: 100px;
  padding: 2px;
  transition: background 0.6s;

  .draggable {
    width: $button-size;
    height: $button-size;
    background: $button-color;
    border-radius: 100%;
    transform: translateX(0%);
    transition: transform 0.05s ease-in-out;
  }

  &.active {
    background: $background-active;
    transition: background 0.6s;
  }
}

.switches {
  margin-right: 30px
}

</style>

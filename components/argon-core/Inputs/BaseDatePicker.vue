<template>
  <base-input>
      <flat-picker slot-scope="{focus, blur}"
                  @on-open="focus"
                  @on-close="blur"
                  :config="config"
                  class="form-control datepicker"
                  :placeholder="label"
                  v-model="fields.date_created"
                  />

  </base-input>
</template>

<script>
import flatPicker from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import {Vietnamese} from 'flatpickr/dist/l10n/vn.js';

export default {
  name: "BaseDatePicker",
  components: {flatPicker},
  props: ['fields','label'],
  data(){
    return {
      date_created: "",
      config: {
        wrap: false, // set wrap to true only when using 'input-group'
        altFormat: 'd/m/Y',
        altInput: true,
        dateFormat: 'd/m/Y',
        allowInput: true,
        locale: Vietnamese, // locale for this instance only
      }
    }
  }
}
</script>

<style scoped>

</style>

<template>
  <div class="card">
    <div class="card-header">
      <div class="row align-items-center">
        <div class="col-lg-6 col-6">
          <h3 class="mb-0">{{ form_name }}</h3>
        </div>
        <div class="col-lg-6 col-6 text-right">
          <slot name="header-button"></slot>
        </div>
      </div>
    </div>

      <div class="card-body">
        <form :action="form_action" :method="form_method"></form>
      </div>
    <div class="card-footer py-4 d-flex justify-content-end">
    </div>
  </div>
</template>
<script>
export default {
  name: 'base-form',
  components: {},
  props: {
    method:String,
    action: String,
    name: String
  },
  data() {
    return {
      form_name: this.name,
      form_action: this.action || "#",
      form_method: this.method || "GET"
    };
  }
}
</script>

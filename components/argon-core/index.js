import BaseAlert from './BaseAlert.vue';
import IconCheckbox from './Inputs/IconCheckbox.vue';
import BaseInput from './Inputs/BaseInput.vue';
import BaseDatePicker from './Inputs/BaseDatePicker.vue';
import Badge from './Badge';
import BaseProgress from './BaseProgress.vue';
import BaseButton from './BaseButton.vue';

import BaseDropdown from './BaseDropdown.vue';

import Card from './Cards/Card.vue';
import StatsCard from './Cards/StatsCard.vue';
import BaseNav from './Navbar/BaseNav';

import Breadcrumb from './Breadcrumb/Breadcrumb.vue';
import BreadcrumbItem from './Breadcrumb/BreadcrumbItem.vue';
import RouteBreadCrumb from './Breadcrumb/RouteBreadcrumb.vue';
import Collapse from './Collapse/Collapse.vue';
import CollapseItem from './Collapse/CollapseItem.vue';
import Modal from './Modal.vue';
import LoadingPanel from './LoadingPanel.vue';
import BaseCheckbox from '@/components/argon-core/Inputs/BaseCheckbox';

import BasePagination from './BasePagination.vue';

import SidebarPlugin from './SidebarPlugin';
import BaseFormGroup from "@/components/argon-core/Inputs/BaseFormGroup";
import BaseTextArea from "@/components/argon-core/Inputs/BaseTextArea";

export {
    IconCheckbox,
    Badge,
    BaseAlert,
    BaseProgress,
    BaseDatePicker,
    BasePagination,
    BaseInput,
    Card,
    StatsCard,
    BaseDropdown,
    SidebarPlugin,
    BaseNav,
    Breadcrumb,
    BreadcrumbItem,
    RouteBreadCrumb,
    Modal,
    BaseButton,
    Collapse,
    CollapseItem,
    LoadingPanel,
    BaseFormGroup,
    BaseTextArea,
    BaseCheckbox,
};

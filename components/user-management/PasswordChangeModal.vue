<template>
  <div v-if="show">
    <!-- Modal -->
    <div class="modal fade show" style="display: block;" tabindex="-1" aria-labelledby="passwordChangeModalLabel">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="passwordChangeModalLabel">
              Thay đổi mật khẩu - {{ userName }}
            </h5>
            <button type="button" class="btn-close" aria-label="Close" @click="closeModal"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="handleSubmit">
              <!-- Mật khẩu mới -->
              <div class="mb-3">
                <label class="form-control-label">Mật khẩu mới <span class="text-danger">*</span></label>
                <div class="input-group">
                  <input
                    v-model="form.password"
                    :type="showPassword ? 'text' : 'password'"
                    class="form-control"
                    :class="{ 'is-invalid': errors.password }"
                    placeholder="Nhập mật khẩu mới (8-30 ký tự, gồm chữ và số)"
                  />
                  <button
                    type="button"
                    class="btn btn-outline-secondary"
                    @click="togglePasswordVisibility"
                  >
                    <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                  </button>
                </div>
                <div v-if="errors.password" class="invalid-feedback d-block">
                  {{ errors.password }}
                </div>
              </div>

              <!-- Xác nhận mật khẩu -->
              <div class="mb-3">
                <label class="form-control-label">Xác nhận mật khẩu <span class="text-danger">*</span></label>
                <input
                  v-model="form.confirmPassword"
                  :type="showPassword ? 'text' : 'password'"
                  class="form-control"
                  :class="{ 'is-invalid': errors.confirmPassword }"
                  placeholder="Nhập lại mật khẩu để xác nhận"
                />
                <div v-if="errors.confirmPassword" class="invalid-feedback d-block">
                  {{ errors.confirmPassword }}
                </div>
              </div>

              <!-- Password generation button -->
              <div class="mb-3">
                <button
                  type="button"
                  class="btn btn-sm btn-primary"
                  @click="generatePassword"
                  title="Tạo mật khẩu tự động"
                >
                  <i class="fas fa-magic"></i> Tạo mật khẩu tự động
                </button>
                <small class="text-muted d-block mt-2">
                  Mật khẩu phải có 8-30 ký tự, bao gồm cả chữ và số
                </small>
              </div>

              <!-- Thông báo lỗi chung -->
              <div v-if="generalError" class="alert alert-danger">
                {{ generalError }}
              </div>

              <!-- Thông báo thành công -->
              <div v-if="successMessage" class="alert alert-success">
                {{ successMessage }}
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="closeModal" :disabled="isLoading">
              Hủy
            </button>
            <button 
              type="button" 
              class="btn btn-primary" 
              @click="handleSubmit"
              :disabled="isLoading || !form.password || !form.confirmPassword"
            >
              {{ isLoading ? 'Đang cập nhật...' : 'Thay đổi mật khẩu' }}
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- Backdrop -->
    <div class="modal-backdrop fade show" @click="closeModal"></div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import useUsers from '~/composition/useUsers'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  userId: {
    type: [String, Number],
    required: true
  },
  userName: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['close', 'success'])

const { changePassword } = useUsers()

// Form data
const form = ref({
  password: '',
  confirmPassword: ''
})

// State
const isLoading = ref(false)
const errors = ref({})
const generalError = ref('')
const successMessage = ref('')
const showPassword = ref(false)

// Watch for modal show/hide to reset form
watch(() => props.show, (newShow) => {
  if (newShow) {
    resetForm()
  }
})

// Reset form
function resetForm() {
  form.value = {
    password: '',
    confirmPassword: ''
  }
  errors.value = {}
  generalError.value = ''
  successMessage.value = ''
  showPassword.value = false
}

// Toggle password visibility
function togglePasswordVisibility() {
  showPassword.value = !showPassword.value
}

// Generate password
function generatePassword() {
  const length = 12
  const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
  let password = ""
  
  // Ensure password has at least one number and one letter
  password += charset.charAt(Math.floor(Math.random() * 26)) // lowercase letter
  password += charset.charAt(Math.floor(Math.random() * 26) + 26) // uppercase letter
  password += charset.charAt(Math.floor(Math.random() * 10) + 52) // number
  
  // Fill the rest randomly
  for (let i = 3; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length))
  }
  
  // Shuffle the password
  password = password.split('').sort(() => 0.5 - Math.random()).join('')
  
  form.value.password = password
  form.value.confirmPassword = password
}

// Validate form
function validateForm() {
  errors.value = {}
  let isValid = true

  // Password validation
  if (!form.value.password) {
    errors.value.password = 'Mật khẩu là bắt buộc'
    isValid = false
  } else if (form.value.password.length < 8 || form.value.password.length > 30) {
    errors.value.password = 'Mật khẩu phải có từ 8-30 ký tự'
    isValid = false
  } else if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(form.value.password)) {
    errors.value.password = 'Mật khẩu phải có cả chữ và số'
    isValid = false
  }

  // Confirm password validation
  if (!form.value.confirmPassword) {
    errors.value.confirmPassword = 'Xác nhận mật khẩu là bắt buộc'
    isValid = false
  } else if (form.value.password !== form.value.confirmPassword) {
    errors.value.confirmPassword = 'Mật khẩu xác nhận không khớp'
    isValid = false
  }

  return isValid
}

// Handle form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  isLoading.value = true
  generalError.value = ''
  successMessage.value = ''

  try {
    await changePassword(props.userId, {
      password: form.value.password,
      confirmPassword: form.value.confirmPassword
    })
    
    successMessage.value = 'Thay đổi mật khẩu thành công!'
    
    // Emit success event and close modal after a short delay
    setTimeout(() => {
      emit('success')
      closeModal()
    }, 1500)
    
  } catch (error) {
    console.error('Change password error:', error)
    if (error.response?.data?.message) {
      generalError.value = error.response.data.message
    } else {
      generalError.value = 'Có lỗi xảy ra khi thay đổi mật khẩu'
    }
  } finally {
    isLoading.value = false
  }
}

// Close modal
function closeModal() {
  emit('close')
}
</script>

<style scoped>
.modal {
  z-index: 1050;
}

.modal-backdrop {
  z-index: 1040;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  font-weight: bold;
  color: #000;
  opacity: 0.5;
}

.btn-close:hover {
  opacity: 0.75;
}
</style> 
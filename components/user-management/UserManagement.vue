<template>
  <div v-if="!$permissions.canViewUsers()" class="row mt-4 mb-4">
    <div class="col-lg-12">
      <div class="alert alert-warning">
        <h4>Không có quyền truy cập</h4>
        <p>Bạn không có quyền xem danh sách người dùng. Vui lòng liên hệ quản trị viên để được cấp quyền.</p>
      </div>
    </div>
  </div>
  <div v-else class="row mt-4 mb-4">
    <div class="col-lg-6 mb-4">
      <Card>
        <template #header>
          <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Danh sách người dùng</h4>
            <BaseButton v-if="$permissions.canCreateUser()" type="primary" size="sm" @click="navigateToCreate">
              + <PERSON><PERSON><PERSON> người dùng
            </BaseButton>
          </div>
        </template>
        <div v-if="isLoading" class="d-flex justify-content-center align-items-center" style="min-height: 120px;">
          <span>Đang tải dữ liệu người dùng...</span>
        </div>
        <UserList v-else :users="users" :page="page" :limit="limit" :total="total" @select="selectUser"
          @page-change="handlePageChange" />
      </Card>
    </div>
    <div class="col-lg-6 mb-4">
      <Card>
        <template #header>
          <h4 class="mb-0">Chi tiết người dùng</h4>
        </template>
        <UserDetail :userId="selectedUserId" />
        <div v-if="selectedUserId" class="mt-3">
          <div class="d-flex align-items-center">
            <UserRoleAssign :roles="roles" :user="selectedUser" :isLoading="isUpdatingRole"
              @update-role="updateUserRole" @delete-user="onDeleteUser">
              <template #submit-button>
                <BaseButton type="primary" native-type="submit">Cập nhật Quyền</BaseButton>
              </template>

            </UserRoleAssign>

          </div>
        </div>
      </Card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from '@nuxtjs/composition-api'
import UserList from './UserList.vue'
import UserDetail from './UserDetail.vue'
import UserRoleAssign from './UserRoleAssign.vue'
import useUsers from '~/composition/useUsers'
import Card from '@/components/argon-core/Cards/Card.vue'
import BaseButton from '@/components/argon-core/BaseButton.vue'

const router = useRouter()

const users = ref([])
const roles = ref([])
const selectedUser = ref(null)
const selectedUserId = ref(null)
const isLoading = ref(false)
const hasError = ref(false)
const isUpdatingRole = ref(false)
const page = ref(1)
const limit = ref(20)
const total = ref(0)

const { fetchUsers, fetchRoles, updateUserRole: apiUpdateUserRole, deleteUser } = useUsers()

async function loadData(customPage = page.value) {
  isLoading.value = true
  hasError.value = false
  try {
    const res = await fetchUsers({ page: customPage, limit: limit.value })
    console.log(res.data)
    users.value = res.data || []
    total.value = res.total || res.totalCount || 0
    page.value = res.page || customPage
    roles.value = await fetchRoles()
  } catch (e) {
    hasError.value = true
  } finally {
    isLoading.value = false
  }
}

onMounted(() => loadData())

function selectUser(user) {
  selectedUser.value = user
  selectedUserId.value = user.id
}

function handlePageChange(newPage) {
  loadData(newPage)
}

function navigateToCreate() {
  console.log('Navigating to create user page...')
  router.push('/user-management/create')
}

async function updateUserRole(roleId) {
  if (!selectedUser.value) return
  isUpdatingRole.value = true
  try {
    await apiUpdateUserRole(selectedUser.value.id, roleId)
    const role = roles.value.find(r => r.id === Number(roleId))
    if (role) selectedUser.value.role = role
  } catch (e) {
    // handle error nếu cần
  } finally {
    isUpdatingRole.value = false
  }
}

async function onDeleteUser() {
  if (!selectedUser.value) return
  if (!window.confirm('Bạn có chắc chắn muốn xóa user này?')) return
  try {
    await deleteUser(selectedUser.value.id)
    await loadData()
    selectedUser.value = null
    selectedUserId.value = null
  } catch (e) {
    // handle error nếu cần
  }
}
</script>
<template>
  <div v-if="!$permissions.canUpdateUser()" class="row mt-4 mb-4">
    <div class="col-lg-12">
      <div class="alert alert-danger">
        <h4>Không có quyền truy cập</h4>
        <p>Bạn không có quyền chỉnh sửa thông tin người dùng. Vui lòng liên hệ quản trị viên để được cấp quyền.</p>
        <BaseButton type="secondary" @click="$router.push('/user-management')">
          ← Quay lại danh sách
        </BaseButton>
      </div>
    </div>
  </div>
  <div v-else class="row mt-4 mb-4">
    <div class="col-lg-12">
      <Card>
        <template #header>
          <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Chỉnh sửa người dùng</h4>
            <BaseButton
              type="secondary"
              size="sm"
              @click="$router.push('/user-management')"
            >
              ← Quay lại
            </BaseButton>
          </div>
        </template>

        <div v-if="isLoadingUser" class="d-flex justify-content-center align-items-center p-5">
          <span>Đang tải thông tin người dùng...</span>
        </div>

        <form v-else @submit.prevent="handleSubmit" class="p-3">
          <div class="row">
            <!-- Tên người dùng -->
            <div class="col-md-6 mb-3">
              <label class="form-control-label">Họ và tên <span class="text-danger">*</span></label>
              <BaseInput
                v-model="form.name"
                placeholder="Nhập họ và tên"
                :error="errors.name"
              />
            </div>

            <!-- Email -->
            <div class="col-md-6 mb-3">
              <label class="form-control-label">Email <span class="text-danger">*</span></label>
              <BaseInput
                v-model="form.email"
                type="email"
                placeholder="Nhập email"
                :error="errors.email"
              />
            </div>

            <!-- Số điện thoại -->
            <div class="col-md-6 mb-3">
              <label class="form-control-label">Số điện thoại</label>
              <BaseInput
                v-model="form.phone"
                type="number"
                placeholder="Nhập số điện thoại"
                :error="errors.phone"
              />
            </div>

            <!-- Campaign (Fixed) -->
            <div class="col-md-6 mb-3">
              <label class="form-control-label">Campaign ID</label>
              <BaseInput
                v-model="form.campaignId"
                placeholder="Campaign ID"
                :error="errors.campaignId"
              />
            </div>

            <!-- Role -->
            <div class="col-md-6 mb-3">
              <label class="form-control-label">Vai trò <span class='text-danger'>*</span></label>
              <el-select
                v-model="form.roleId"
                placeholder="Chọn vai trò"
                style="width: 100%"
                :class="{ 'is-invalid': errors.roleId }"
              >
                <el-option
                  v-for="role in roles"
                  :key="role.id"
                  :label="role.displayName || role.name"
                  :value="role.id"
                />
              </el-select>
              <div v-if="errors.roleId" class="invalid-feedback">
                {{ errors.roleId }}
              </div>
            </div>

            <!-- Trạng thái -->
            <div class="col-md-6 mb-3">
              <label class="form-control-label">Trạng thái</label>
              <el-select
                v-model="form.status"
                placeholder="Chọn trạng thái"
                style="width: 100%"
                :class="{ 'is-invalid': errors.status }"
              >
                <el-option
                  label="Hoạt động"
                  value="1"
                />
                <el-option
                  label="Không hoạt động"
                  value="0"
                />
              </el-select>
              <div v-if="errors.status" class="invalid-feedback">
                {{ errors.status }}
              </div>
            </div>
          </div>

          <!-- Password Change Button -->
          <div class="mb-3">
            <label class="form-control-label">Mật khẩu</label>
            <div class="d-flex align-items-center">
              <BaseButton
                type="warning"
                size="sm"
                @click="openPasswordModal"
                class="me-2"
              >
                <i class="fas fa-key"></i> Thay đổi mật khẩu
              </BaseButton>
              <small class="text-muted">
                Nhấn để thay đổi mật khẩu người dùng
              </small>
            </div>
          </div>

          <!-- Thông báo lỗi chung -->
          <div v-if="generalError" class="alert alert-danger mb-3">
            {{ generalError }}
          </div>

          <!-- Thông báo thành công -->
          <div v-if="successMessage" class="alert alert-success mb-3">
            {{ successMessage }}
          </div>

          <!-- Buttons -->
          <div class="d-flex justify-content-end">
            <BaseButton
              type="secondary"
              class="me-2"
              @click="$router.push('/user-management')"
            >
              Hủy
            </BaseButton>
            <BaseButton
              type="primary"
              native-type="submit"
              :disabled="isLoading"
            >
              {{ isLoading ? 'Đang cập nhật...' : 'Cập nhật người dùng' }}
            </BaseButton>
          </div>
        </form>
      </Card>

      <!-- Password Change Modal -->
      <PasswordChangeModal
        :show="showPasswordModal"
        :user-id="props.userId"
        :user-name="user?.name || 'Người dùng'"
        @close="closePasswordModal"
        @success="onPasswordChangeSuccess"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from '@nuxtjs/composition-api'
import useUsers from '~/composition/useUsers'
import Card from '@/components/argon-core/Cards/Card.vue'
import BaseButton from '@/components/argon-core/BaseButton.vue'
import BaseInput from '@/components/argon-core/Inputs/BaseInput.vue'
import PasswordChangeModal from '@/components/user-management/PasswordChangeModal.vue'

const props = defineProps({
  userId: {
    type: [String, Number],
    required: true
  }
})

const router = useRouter()
const { updateUser, fetchUserDetail, fetchRoles } = useUsers()

// Form data
const form = ref({
  name: '',
  email: '',
  phone: '',
  campaignId: '',
  roleId: '',
  status: '1'
})

// State
const isLoading = ref(false)
const isLoadingUser = ref(false)
const errors = ref({})
const generalError = ref('')
const successMessage = ref('')
const roles = ref([])
const user = ref(null)
const showPasswordModal = ref(false)

// Load initial data and user details
onMounted(async () => {
  await loadInitialData()
  await loadUserData()
})

// Watch for userId changes (if navigating between different user edit pages)
watch(() => props.userId, () => {
  loadUserData()
})

async function loadInitialData() {
  try {
    const rolesData = await fetchRoles()
    roles.value = rolesData || []
  } catch (error) {
    console.error('Failed to load initial data:', error)
  }
}

async function loadUserData() {
  if (!props.userId) return
  
  isLoadingUser.value = true
  try {
    const userData = await fetchUserDetail(props.userId)
    user.value = userData
    
    // Populate form with user data
    form.value = {
      name: userData.name || '',
      email: userData.email || '',
      phone: userData.phone ? userData.phone.toString() : '',
      campaignId: '68', // Always set to fixed campaign ID
      roleId: userData.roleId || '',
      status: userData.status ? userData.status.toString() : '1'
    }
  } catch (error) {
    console.error('Failed to load user data:', error)
    generalError.value = 'Không thể tải thông tin người dùng'
  } finally {
    isLoadingUser.value = false
  }
}

// Validate form
function validateForm() {
  errors.value = {}
  let isValid = true

  if (!form.value.name) {
    errors.value.name = 'Họ và tên là bắt buộc'
    isValid = false
  }

  if (!form.value.email) {
    errors.value.email = 'Email là bắt buộc'
    isValid = false
  } else if (!/\S+@\S+\.\S+/.test(form.value.email)) {
    errors.value.email = 'Email không hợp lệ'
    isValid = false
  }

  // Phone is not required

  if (!form.value.roleId) {
    errors.value.roleId = 'Vai trò là bắt buộc'
    isValid = false
  }

  return isValid
}

// Handle form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  isLoading.value = true
  generalError.value = ''
  successMessage.value = ''

  try {
    // Prepare data for API - only send changed fields
    const userData = {}
    
    if (form.value.name !== user.value.name) {
      userData.name = form.value.name
    }
    
    if (form.value.email !== user.value.email) {
      userData.email = form.value.email
    }
    
    if (parseInt(form.value.phone) !== user.value.phone) {
      userData.phone = parseInt(form.value.phone)
    }
    
    // Always set campaign ID to 68
    userData.campaignId = 68
    
    if (parseInt(form.value.roleId) !== user.value.roleId) {
      userData.roleId = parseInt(form.value.roleId)
    }
    
    if (parseInt(form.value.status) !== user.value.status) {
      userData.status = parseInt(form.value.status)
    }

    // Only send request if there are changes
    if (Object.keys(userData).length === 0) {
      successMessage.value = 'Không có thay đổi nào để cập nhật'
      return
    }

    await updateUser(props.userId, userData)
    
    successMessage.value = 'Cập nhật người dùng thành công!'
    
    // Reload user data to reflect changes
    await loadUserData()
    
  } catch (error) {
    console.error('Update user error:', error)
    if (error.response?.data?.message) {
      generalError.value = error.response.data.message
    } else {
      generalError.value = 'Có lỗi xảy ra khi cập nhật người dùng'
    }
  } finally {
    isLoading.value = false
  }
}

// Password modal functions
function openPasswordModal() {
  showPasswordModal.value = true
}

function closePasswordModal() {
  showPasswordModal.value = false
}

function onPasswordChangeSuccess() {
  successMessage.value = 'Mật khẩu đã được thay đổi thành công!'
  
  // Clear success message after a few seconds
  setTimeout(() => {
    successMessage.value = ''
  }, 5000)
}
</script> 
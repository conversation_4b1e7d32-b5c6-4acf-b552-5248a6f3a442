<template>
  <div v-if="!$permissions.canViewUsers()" class="alert alert-warning">
    Bạn không có quyền xem thông tin người dùng.
  </div>
  <div v-else-if="isLoading" class="text-center py-4">
    <span><PERSON>ang tải thông tin user...</span>
  </div>
  <div v-else-if="error" class="text-center py-4 text-danger">
    <span>Lỗi khi tải thông tin user</span>
  </div>
  <div v-else-if="user" class="card-body">
    <div class="mb-3">
      <div><strong>ID:</strong> {{ user.id }}</div>
      <div><strong>Email:</strong> {{ user.email }}</div>
      <div><strong>Role:</strong> {{ user.role?.id || '-' }} - {{ user.role?.displayName || '-' }}</div>
    </div>
    <div>
      <strong>Permissions:</strong>
      <div v-if="groupedPermissions && Object.keys(groupedPermissions).length">
        <div v-for="(permissions, resource) in groupedPermissions" :key="resource" class="mb-3">
          <div class="font-weight-bold text-primary mb-1">{{ resource }}</div>
          <ul class="list-group list-group-flush">
            <li v-for="p in permissions" :key="p.id" class="list-group-item py-1 px-2">
              <span>{{ p.id || '-' }} - {{ p.displayName }}</span>
              <span v-if="p.description" class="text-muted small ml-2">- {{ p.description }}</span>
            </li>
          </ul>
        </div>
      </div>
      <div v-else class="text-muted">Không có quyền nào</div>
    </div>
  </div>
  <div v-else class="text-center py-4">Chọn user để xem chi tiết</div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import useUsers from '~/composition/useUsers'

const props = defineProps({
  userId: {
    type: [Number, String],
    default: null
  }
})

const user = ref(null)
const isLoading = ref(false)
const error = ref(null)

const { fetchUserDetail } = useUsers()

const groupedPermissions = computed(() => {
  if (!user.value || !Array.isArray(user.value.permissions)) return {}
  return user.value.permissions.reduce((acc, p) => {
    if (!p.resource) return acc
    if (!acc[p.resource]) acc[p.resource] = []
    acc[p.resource].push(p)
    return acc
  }, {})
})

async function loadUserDetail() {
  if (!props.userId) {
    user.value = null
    return
  }

  isLoading.value = true
  error.value = null
  
  try {
    const userDetail = await fetchUserDetail(props.userId)
    user.value = userDetail
  } catch (e) {
    console.error('Error fetching user details:', e)
    error.value = e
  } finally {
    isLoading.value = false
  }
}

// Watch for changes in userId prop
watch(() => props.userId, (newUserId) => {
  loadUserDetail()
}, { immediate: true })
</script> 
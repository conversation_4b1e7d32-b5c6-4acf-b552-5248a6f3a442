<template>
  <div v-if="!$permissions.canCreateUser()" class="row mt-4 mb-4">
    <div class="col-lg-12">
      <div class="alert alert-danger">
        <h4>Không có quyền truy cập</h4>
        <p>Bạn không có quyền tạo người dùng mới. Vui lòng liên hệ quản trị viên để được cấp quyền.</p>
        <BaseButton type="secondary" @click="$router.push('/user-management')">
          ← Quay lại danh sách
        </BaseButton>
      </div>
    </div>
  </div>
  <div v-else class="row mt-4 mb-4">
    <div class="col-lg-12">
      <Card>
        <template #header>
          <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Tạo người dùng mới</h4>
            <BaseButton
              type="secondary"
              size="sm"
              @click="$router.push('/user-management')"
            >
              ← Quay lại
            </BaseButton>
          </div>
        </template>

        <form @submit.prevent="handleSubmit" class="p-3">
          <div class="row">
            <!-- Tên người dùng -->
            <div class="col-md-6 mb-3">
              <label class="form-control-label">Họ và tên <span class="text-danger">*</span></label>
              <BaseInput
                v-model="form.name"
                placeholder="Nhập họ và tên"
                :error="errors.name"
              />
            </div>

            <!-- Email -->
            <div class="col-md-6 mb-3">
              <label class="form-control-label">Email <span class="text-danger">*</span></label>
              <BaseInput
                v-model="form.email"
                type="email"
                placeholder="Nhập email"
                :error="errors.email"
              />
            </div>

            <!-- Số điện thoại -->
            <div class="col-md-6 mb-3">
              <label class="form-control-label">Số điện thoại</label>
              <BaseInput
                v-model="form.phone"
                type="number"
                placeholder="Nhập số điện thoại"
                :error="errors.phone"
              />
            </div>

            <!-- Campaign (Fixed) -->
            <div class="col-md-6 mb-3">
              <label class="form-control-label">Campaign ID</label>
              <BaseInput
                v-model="form.campaignId"
                placeholder="Campaign ID"
                :error="errors.campaignId"
              />
            </div>

            <!-- Role -->
            <div class="col-md-6 mb-3">
              <label class="form-control-label">Vai trò <span class='text-danger'>*</span></label>
              <el-select
                v-model="form.roleId"
                placeholder="Chọn vai trò"
                style="width: 100%"
                :class="{ 'is-invalid': errors.roleId }"
              >
                <el-option
                  v-for="role in roles"
                  :key="role.id"
                  :label="role.displayName || role.name"
                  :value="role.id"
                />
              </el-select>
              <div v-if="errors.roleId" class="invalid-feedback">
                {{ errors.roleId }}
              </div>
            </div>

            <!-- Trạng thái -->
            <div class="col-md-6 mb-3">
              <label class="form-control-label">Trạng thái</label>
              <el-select
                v-model="form.status"
                placeholder="Chọn trạng thái"
                style="width: 100%"
                :class="{ 'is-invalid': errors.status }"
              >
                <el-option
                  label="Hoạt động"
                  value="1"
                />
                <el-option
                  label="Không hoạt động"
                  value="0"
                />
              </el-select>
              <div v-if="errors.status" class="invalid-feedback">
                {{ errors.status }}
              </div>
            </div>

            <!-- Mật khẩu -->
            <div class="col-md-6 mb-3">
              <label class="form-control-label">Mật khẩu <span class="text-danger">*</span></label>
              <input
                v-model="form.pin"
                :type="showPassword ? 'text' : 'password'"
                class="form-control"
                :class="{ 'is-invalid': errors.pin }"
                placeholder="Nhập mật khẩu (8-30 ký tự, gồm chữ và số)"
              />
              <div v-if="errors.pin" class="invalid-feedback d-block">
                {{ errors.pin }}
              </div>
            </div>

            <!-- Xác nhận mật khẩu -->
            <div class="col-md-6 mb-3">
              <label class="form-control-label">Xác nhận mật khẩu <span class="text-danger">*</span></label>
              <input
                v-model="form.confirmPin"
                :type="showPassword ? 'text' : 'password'"
                class="form-control"
                :class="{ 'is-invalid': errors.confirmPin }"
                placeholder="Nhập lại mật khẩu để xác nhận"
              />
              <div v-if="errors.confirmPin" class="invalid-feedback d-block">
                {{ errors.confirmPin }}
              </div>
            </div>

            <!-- Password Controls -->
            <div class="col-md-12 mb-3">
              <div class="d-flex justify-content-end gap-2">
                <button
                  type="button"
                  class="btn btn-sm btn-outline-secondary"
                  @click="togglePasswordVisibility"
                  title="Hiện/Ẩn mật khẩu"
                >
                  <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                  {{ showPassword ? 'Ẩn mật khẩu' : 'Hiện mật khẩu' }}
                </button>
                <button
                  type="button"
                  class="btn btn-sm btn-primary"
                  @click="generatePassword"
                  title="Tạo mật khẩu tự động"
                >
                  <i class="fas fa-magic"></i> Tạo mật khẩu tự động
                </button>
              </div>
              <small class="text-muted d-block mt-2">Mật khẩu phải có 8-30 ký tự, bao gồm cả chữ và số</small>
            </div>
          </div>

          <!-- Thông báo lỗi chung -->
          <div v-if="generalError" class="alert alert-danger mb-3">
            {{ generalError }}
          </div>

          <!-- Buttons -->
          <div class="d-flex justify-content-end">
            <BaseButton
              type="secondary"
              class="me-2"
              @click="$router.push('/user-management')"
            >
              Hủy
            </BaseButton>
            <BaseButton
              type="primary"
              native-type="submit"
              :disabled="isLoading"
            >
              {{ isLoading ? 'Đang tạo...' : 'Tạo người dùng' }}
            </BaseButton>
          </div>
        </form>
      </Card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from '@nuxtjs/composition-api'
import useUsers from '~/composition/useUsers'
import Card from '@/components/argon-core/Cards/Card.vue'
import BaseButton from '@/components/argon-core/BaseButton.vue'
import BaseInput from '@/components/argon-core/Inputs/BaseInput.vue'

const router = useRouter()
const { createUser, fetchRoles } = useUsers()

// Form data
const form = ref({
  name: '',
  email: '',
  phone: '',
  campaignId: '68',
  roleId: '',
  pin: '',
  confirmPin: '',
  status: '1'
})

// State
const isLoading = ref(false)
const errors = ref({})
const generalError = ref('')
const roles = ref([])
const showPassword = ref(false)

// Load initial data
onMounted(async () => {
  try {
    const rolesData = await fetchRoles()
    roles.value = rolesData || []
  } catch (error) {
    console.error('Failed to load initial data:', error)
  }
})

// Password functions
function togglePasswordVisibility() {
  showPassword.value = !showPassword.value
}

function generatePassword() {
  const length = 12
  const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
  let password = ""
  
  // Ensure password has at least one number and one letter
  password += charset.charAt(Math.floor(Math.random() * 26)) // lowercase letter
  password += charset.charAt(Math.floor(Math.random() * 26) + 26) // uppercase letter
  password += charset.charAt(Math.floor(Math.random() * 10) + 52) // number
  
  // Fill the rest randomly
  for (let i = 3; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length))
  }
  
  // Shuffle the password
  password = password.split('').sort(() => 0.5 - Math.random()).join('')
  
  form.value.pin = password
  form.value.confirmPin = password
}

// Validate form
function validateForm() {
  errors.value = {}
  let isValid = true

  if (!form.value.name) {
    errors.value.name = 'Họ và tên là bắt buộc'
    isValid = false
  }

  if (!form.value.email) {
    errors.value.email = 'Email là bắt buộc'
    isValid = false
  } else if (!/\S+@\S+\.\S+/.test(form.value.email)) {
    errors.value.email = 'Email không hợp lệ'
    isValid = false
  }

  // Phone is not required

  if (!form.value.roleId) {
    errors.value.roleId = 'Vai trò là bắt buộc'
    isValid = false
  }

  // Password validation
  if (!form.value.pin) {
    errors.value.pin = 'Mật khẩu là bắt buộc'
    isValid = false
  } else if (form.value.pin.length < 8 || form.value.pin.length > 30) {
    errors.value.pin = 'Mật khẩu phải có từ 8-30 ký tự'
    isValid = false
  } else if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(form.value.pin)) {
    errors.value.pin = 'Mật khẩu phải có cả chữ và số'
    isValid = false
  }

  // Confirm password validation
  if (!form.value.confirmPin) {
    errors.value.confirmPin = 'Xác nhận mật khẩu là bắt buộc'
    isValid = false
  } else if (form.value.pin !== form.value.confirmPin) {
    errors.value.confirmPin = 'Mật khẩu xác nhận không khớp'
    isValid = false
  }

  return isValid
}

// Handle form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  isLoading.value = true
  generalError.value = ''

  try {
    // Prepare data for API
    const userData = {
      name: form.value.name,
      email: form.value.email,
      phone: parseInt(form.value.phone),
      campaignId: form.value.campaignId ? parseInt(form.value.campaignId) : undefined,
      roleId: parseInt(form.value.roleId),
      pin: form.value.pin,
      status: parseInt(form.value.status)
    }

    // Remove undefined values
    Object.keys(userData).forEach(key => {
      if (userData[key] === undefined) {
        delete userData[key]
      }
    })

    await createUser(userData)
    
    // Success - redirect to user management
    router.push('/user-management')
  } catch (error) {
    console.error('Create user error:', error)
    if (error.response?.data?.message) {
      generalError.value = error.response.data.message
    } else {
      generalError.value = 'Có lỗi xảy ra khi tạo người dùng'
    }
  } finally {
    isLoading.value = false
  }
}
</script> 
<template>
  <form v-if="$permissions.canUpdateUser() || $permissions.canDeleteUser()" @submit.prevent="onSubmit" class="w-100">
    <div v-if="$permissions.canUpdateUser()" class="form-group mb-4">
      <label>Chọn role mới</label>
      <el-select v-model="selectedRoleId" class="w-50" :disabled="isLoading">
        <el-option 
          v-for="role in roles" 
          :key="role.id" 
          :value="role.id"
          :label="`${role.displayName} - ${role.description}`"
        />
      </el-select>
    </div>
    <div class="d-flex w-100 align-items-center justify-content-between">
      <BaseButton 
        v-if="$permissions.canUpdateUser()"
        :disabled="isLoading" 
        :loading="isLoading" 
        type="primary" 
        native-type="submit" 
        class="mt-2"
      >
        <span v-if="!isLoading">Cậ<PERSON> nh<PERSON><PERSON></span>
        <span v-else>
          <PERSON><PERSON> cập nhật
        </span>
      </BaseButton>
      <BaseButton 
        v-if="user.status > 0 && $permissions.canDeleteUser()" 
        type="danger" 
        class="ml-2" 
        @click="onDeleteUser" 
        :disabled="isLoading"
      >
        Xóa user
      </BaseButton>
    </div>
  </form>
  <div v-else class="alert alert-warning">
    Bạn không có quyền quản lý người dùng này.
  </div>
</template>

<script setup>
import BaseButton from '@/components/argon-core/BaseButton.vue'
import { ref, watch } from 'vue'

const props = defineProps({
  roles: {
    type: Array,
    default: () => []
  },
  user: {
    type: Object,
    required: true
  },
  isLoading: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update-role', 'delete-user'])

const selectedRoleId = ref(props.user.role?.id || '')

watch(() => props.user, (user) => {
  selectedRoleId.value = user.role?.id || ''
})

function onSubmit () {
  emit('update-role', selectedRoleId.value)
}

function onDeleteUser () {
  emit('delete-user')
}
</script>

<style scoped>
.loading-spinner {
  margin-left: 8px;
  width: 18px;
  height: 18px;
  border: 2px solid #ccc;
  border-top: 2px solid #333;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 
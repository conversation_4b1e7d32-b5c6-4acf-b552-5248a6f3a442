<template>
  <div class="card" v-if="listSerials.items">
    <div class="border-0 card-header d-flex justify-content-between">
      <h3 class="mb-0">Danh sách White list</h3>
    </div>

    <el-table
        class="table-responsive"
        border
        size="mini"
        :highlight-current-row="true"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ justifyContent: 'center', fontWeight: 'bold' }"
        :data="listSerials.items"
        style="width: 100%"
    >

      <el-table-column label="ID" width="100px">
        <template v-slot="{ row }">
          {{ row.id }}
        </template>
      </el-table-column>
      <el-table-column label="Code check" min-width="180px">
        <template v-slot="{ row }">
          {{ row.codeCheck }}
        </template>
      </el-table-column>
      <el-table-column label="Campaign" min-width="180px">
        <template>
          {{ currentCampaign.title }}
        </template>
      </el-table-column>
      <el-table-column label="Trạng thái" min-width="160px">

        <template v-slot="{ row }">
          <el-switch
              style="display: block; height: 21px"
              v-model="row.status"
              :active-value="2"
              :inactive-value="1"
              active-text=""
              inactive-text=""
              @change="toggleStatus(row)"
              v-if="$permissions.hasPermission('whitelist.update')"
          >
          </el-switch>

          <div v-else>
            <span v-if="row.status === urboxStatus.ACTIVE.VALUE" class="badge text-wrap badge-success mb-1 lh-120">
            {{ urboxStatus.ACTIVE.TEXT }}
          </span>
            <span v-if="row.status === urboxStatus.DEACTIVE.VALUE" class="badge text-wrap badge-danger mb-1 lh-120">
            {{ urboxStatus.DEACTIVE.TEXT }}
          </span>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!--CARD:FOOTER-->
    <div class="card-footer py-4 d-flex justify-content-end">
      <base-pagination
          v-if="listSerials.items"
          v-model="params.page"
          :total="listSerials.total"
          :per-page="params.limit"
      ></base-pagination>
    </div>
  </div>
</template>

<script>
import {computed, defineComponent, ref, useContext, useFetch, watch} from '@nuxtjs/composition-api';
import {Table, TableColumn, Button, Message, Switch} from 'element-ui';
import {useWhiteList} from '@/composition';
import {URBOX_STATUS} from '@/util/constant.js';
import {whitelistInitState} from '~/store/whitelist';

export default defineComponent({
  name: 'WhiteList',
  components: {
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Button.name]: Button,
    [Switch.name]: Switch,
  },
  props: {
    params: Object,
  },
  setup(props) {
    const {store, $config, $auth} = useContext();
    const {params} = props;

    const listSerials = ref(whitelistInitState);
    const {changeStatus, fetchWhiteList} = useWhiteList();
    const currentCampaign = store.getters['campaignStore/currentCampaign'];
    async function fetchData() {
      await store.dispatch('setLoading', true);
      await fetchWhiteList(params);
      await store.dispatch('setLoading', false);
    }
    watch(
        () => props.params.page,
        async () => {
          await fetchData();
        },
        {
          deep: true
        }
    );
    watch(() => store.getters['whitelist/lists'], () => {
      listSerials.value = store.getters['whitelist/lists']
    })

    useFetch(async () => {
      await fetchData();
    });

    const toggleStatus = async (row) => {
      await changeStatus(row, row.status).then((res) => {
        if (!res) {
          Message({
            message: "Lỗi thay đổi trạng thái",
            type: 'error',
            duration: 5 * 1000,
          });
        }
      })
    }

    return {
      listSerials: listSerials,
      urboxStatus: URBOX_STATUS,
      currentCampaign,
      toggleStatus,
      
    };
  },
});
</script>

<style lang="scss">
.el-table .cell {
  justify-content: center;
}

.el-table__cell:first-child {
  padding: unset !important;

  .cell {
    padding-left: unset !important;
  }
}
</style>

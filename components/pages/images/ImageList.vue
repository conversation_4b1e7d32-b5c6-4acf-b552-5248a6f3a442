<template>
  <div class="card" v-if="imageState.items">
    <div class="border-0 card-header d-flex justify-content-between">
      <h3 class="mb-0">Danh sách ảnh</h3>
      <ExportImage :params="params"/>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-6 col-md-3 text-sm" v-for="(image, index) in imageState.items" :key="index">
          <button @click="handleCopy(image)" class="btn btn-sm btn-success">{{ isCopy && image.filename === isCopy ? 'Copied' : 'Copy' }}</button>
          <img :src="image.url" alt="" width="100%" height="100%"/>
          <div class="text-center">
            {{ image.filename }}
          </div>
          <div class="text-center">{{ convertToDate(image.created) }}</div>
        </div>
      </div>
    </div>
    <!--CARD:FOOTER-->
    <div class="card-footer py-4 d-flex justify-content-end">
      <base-pagination
          v-if="imageState.items"
          v-model="params.page"
          :total="imageState.total"
          :per-page="params.limit"
      ></base-pagination>
    </div>

  </div>
</template>

<script>
import {computed, defineComponent, ref, useContext, useFetch, watch} from '@nuxtjs/composition-api';
import {Table, TableColumn, Button} from 'element-ui';
import ExportImage from '@/components/pages/images/ExportImage.vue';
import useImages from "@/composition/useImages";

export default defineComponent({
  name: 'ImageList',
  components: {
    ExportImage,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Button.name]: Button,
  },
  props: {
    params: Object,
  },
  setup(props) {
    // const store = useStore();
    const {store, $config, $auth} = useContext();
    const {fetchImages} = useImages();


    async function fetchData() {
      await store.dispatch('setLoading', true);
      await fetchImages(props.params);
      await store.dispatch('setLoading', false);
    }

    watch(
        () => props.params.page,
        async () => {
          await fetchData();
        },
    );

    const imageState = computed(() => {
      return store.getters['image/lists'];
    });

    useFetch(async () => {
      await fetchData();
    });


    const convertToDate = (timestamp = null) => {
      if (!timestamp) return '';
      const date = new Date(timestamp * 1000);

      const formattedDate = new Intl.DateTimeFormat('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      }).format(date);

      return formattedDate;
    }
    const isCopy = ref(false);
    const handleCopy = async (image) => {
      try {
        await navigator.clipboard.writeText(image.url);
        isCopy.value = image.filename
      } catch (error) {
        console.error("Failed to copy text: ", error);
      }finally {
        setTimeout(() => isCopy.value = false, 1000);
      }
    }
    return {
      imageState,
      fetchImages,
      convertToDate,
      isCopy,
      handleCopy
    };
  },
})
;
</script>

<style lang="scss">
.el-table .cell {
  justify-content: center;
}

.el-table__cell:first-child {
  padding: unset !important;

  .cell {
    padding-left: unset !important;
  }
}
</style>

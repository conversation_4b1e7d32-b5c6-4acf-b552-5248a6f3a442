<template>
  <div class="row pt-4">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title mb-0">T<PERSON><PERSON> kiếm</h3>
        </div>
        <div class="card-body">
          <form>
            <div class="row">
              <div class="col-lg-6 col-md-6">
                <el-date-picker
                  class="w-100"
                  v-model="dateRange"
                  type="daterange"
                  range-separator="To"
                  start-placeholder="Start date"
                  end-placeholder="End date"
                  format="dd/MM/yyyy"
                  :default-time="['00:00:00', '23:59:59']"
                ></el-date-picker>
              </div>
              <div class="col-lg-3">
                <el-button class="col-lg-6 col-md-6" type="primary" icon="el-icon-search" @click="onSearch()">
                  T<PERSON><PERSON> kiếm
                </el-button>
              </div>
              <div class="col-lg-3">
                <nuxt-link class="btn btn-success" to="/images/create">
                  Thêm mới
                </nuxt-link>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {defineComponent, ref, useContext, watch} from '@nuxtjs/composition-api';
  import { Select, Option, DatePicker, Button } from 'element-ui';
  import moment from 'moment';
import useImages from "@/composition/useImages";

  export default defineComponent({
    name: 'ImageSearchForm',
    props: {
      params: Object,
    },
    components: {
      [Select.name]: Select,
      [Option.name]: Option,
      [DatePicker.name]: DatePicker,
      [Button.name]: Button,
    },
    setup(props) {
      // const store = useStore();
      const { store } = useContext();
      const isLoading = ref(false);
      const { fetchImages } = useImages();

      const dateRange = ref(null);

      watch(dateRange, () => {
        if (dateRange.value && dateRange.value.length) {
          props.params.startDate = moment(dateRange.value[0]).valueOf() / 1000;
          props.params.endDate = moment(dateRange.value[1]).valueOf() / 1000;
        } else {
          props.params.startDate = null;
          props.params.endDate = null;
        }
      });

      const onSearch = async () => {
        await store.dispatch('setLoading', true);
        props.params.page = 1;
        await fetchImages(props.params);
        await store.dispatch('setLoading', false);
      };


      return {
        onSearch,
        dateRange,
        isLoading
      };
    },
  });
</script>

<style lang="scss">
  .el-select .el-input .el-input__inner {
    height: 40px;
  }

  .el-date-editor {
    .el-range-separator {
      width: 10% !important;
    }
    .el-icon-date {
      margin-left: 0;
    }
    i {
      width: 8% !important;
    }
  }
</style>

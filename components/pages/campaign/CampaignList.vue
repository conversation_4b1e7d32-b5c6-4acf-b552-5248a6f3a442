<template>
  <div class="card" v-if="listCampaign">
    <div class="border-0 card-header d-flex justify-content-between">
      <h3 class="mb-0">Danh sách Campaign</h3>
      <el-link v-if="$permissions.hasPermission('campaigns.create')" class="btn btn-success btn-sm text-white text-xs px-2 " href="/campaign/create">
        Tạo campaign
      </el-link>
      <!-- <ExportGift :params="params" /> -->
    </div>
<!-- {{ listCampaign }} -->
    <el-table
        class="table-responsive"
        border
        size="mini"
        :highlight-current-row="true"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ justifyContent: 'center', fontWeight: 'bold' }"
        :data="pagedCampaigns"
        style="width: 100%"
    >
      <!-- <div > -->
      <el-table-column label="Thao tác" width="140px" v-if="$permissions.hasPermission('campaigns.update')">
        <template v-slot="{ row }">
          <!--          <div class="w-100 d-flex" style="padding: 0 12px">-->
          <el-tooltip content="Sửa campaign" placement="top" class="mr-2">
            <NuxtLink :to="'/campaign/' + row.id">
              <el-button circle size="small" type="primary" icon="el-icon-edit"></el-button>
            </NuxtLink>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="ID" width="100px">
        <template v-slot="{ row }">
          {{ row.id }}
        </template>
      </el-table-column>
      <el-table-column label="title" min-width="180px">
        <template v-slot="{ row }">
          {{ row.title ? row.title : '' }}
        </template>
      </el-table-column>
      <el-table-column label="AppId" min-width="160px">
        <template v-slot="{ row }">
          {{ row.app_id ? row.app_id : '' }}
        </template>
      </el-table-column>
      <el-table-column label="Ngày bắt đầu" min-width="180px">
        <template v-slot="{ row }">
          {{ row.start_time ? humanDate(row.start_time) : '' }}
        </template>
      </el-table-column>
      <el-table-column label="Ngày kết thúc" min-width="180px">
        <template v-slot="{ row }">
          {{ row.end_time ? humanDate(row.end_time) : '' }}
        </template>
      </el-table-column>
      <el-table-column label="Ngày kết thúc duyệt" min-width="180px">
        <template v-slot="{ row }">
          {{ row.end_time_for_admin ? humanDate(row.end_time_for_admin) : '' }}
        </template>
      </el-table-column>
      <el-table-column label="Trạng thái" min-width="180px">
        <template v-slot="{ row }">
          <span v-if="row.status === URBOX_STATUS.ACTIVE.VALUE" class="badge text-wrap badge-info mb-1 lh-120">
            {{ URBOX_STATUS.ACTIVE.TEXT }}
          </span>
          <span v-if="row.status === URBOX_STATUS.DEACTIVE.VALUE" class="badge text-wrap badge-info mb-1 lh-120">
            {{ URBOX_STATUS.DEACTIVE.TEXT }}
          </span>
        </template>
      </el-table-column>
      <!-- </div> -->
    </el-table>
    
    <!--CARD:FOOTER-->
    <div class="card-footer py-4 d-flex justify-content-end">
      <div class="pagination">
        <el-pagination
          :current-page="params.page"
          :page-sizes="[20]"
          :page-size="params.limit"
          :total="listCampaign.length"
          @current-change="handlePageChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import {defineComponent, useContext, useFetch, watch, computed} from '@nuxtjs/composition-api';
import {Table, TableColumn, Button, Switch, Message, Link, Pagination} from 'element-ui';
import {useCampaign} from '~/composition';
import {CLAIM_PROCESS, URBOX_STATUS} from '~/util/constant';
// import Pagination from './Pagination.vue';

export default defineComponent({
  name: 'CampaignList',
  components: {
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Button.name]: Button,
    [Switch.name]: Switch,
    [Link.name]: Link,
    [Pagination.name]: Pagination
  },
  props: {
    params: Object,
  },
  setup(props) {
    const {store} = useContext();
    const {fetchCampaignList, listCampaign, changeStatus, isSuccess} = useCampaign();
    const {id: campaignId} = store.getters['campaignStore/currentCampaign'];

    const startIndex = computed(() => (props.params.page - 1) * props.params.limit);
    const endIndex = computed(() => startIndex.value + props.params.limit);
    const pagedCampaigns = computed(() => listCampaign.value.slice(startIndex.value, endIndex.value));
    
    async function fetchData() {
      await store.dispatch('setLoading', true);
      props.params.limit = 20;
      await fetchCampaignList(props.params);
      await store.dispatch('setLoading', false);
    }
    const humanDate = (timestamp) => {
      let date = new Date(timestamp* 1000);
      return date.toLocaleString('vi-VN')
    }
    const toggleStatus = async (row,status) => {
      await changeStatus(row,status).then(() => {
        if(!isSuccess.value) {
          Message({
            message: "Lỗi thay đổi trạng thái",
            type: 'error',
            duration: 5 * 1000,
          });
        }
      })
    }
    const handlePageChange = async (newPage) => {
      console.log(newPage);
      props.params.page = newPage;
      await fetchData();
    };
    watch(
        () => props.params.page,
        async () => {
          await fetchData();
        },
    );
    useFetch(async () => {
      await fetchData();
    });

    return {
      listCampaign,
      claimProcess: CLAIM_PROCESS,
      campaignId,
      toggleStatus,
      URBOX_STATUS,
      humanDate,
      handlePageChange,
      pagedCampaigns
    };
  },
});
</script>

<style lang="scss">
.el-table .cell {
  justify-content: center;
}

.el-table__cell:first-child {
  padding: unset !important;

  .cell {
    padding-left: unset !important;
  }
}
</style>

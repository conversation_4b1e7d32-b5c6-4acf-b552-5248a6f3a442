<template>
  <div>
    <ValidationObserver ref="giftForm" tag="form" v-slot="{ invalid }" @submit.prevent="handleSubmit">
      <div class="row">
        <div class="form-group col-lg-3">
          <ValidationProvider class="form-group" rules="required" v-slot="{ errors }">
            <label for="title">Tên campaign <b class="text-danger">(ID: {{ entity.id }})</b><span
                class="text-danger pl-1">*</span></label>
            <input :class="{ 'is-invalid': errors.length > 0 }" type="text" class="form-control mb-0" id="title"
              placeholder="Tên campaign" required v-model="entity.title">
          </ValidationProvider>

        </div>

        <div class="form-group col-lg-3">

          <ValidationProvider class="form-group" rules="required" v-slot="{ errors }">
            <label for="voucher-code"><PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu submit thông tin<span class="text-danger pl-1">*</span></label>
            <el-date-picker v-model="entity.start_time" format="dd/MM/yyyy HH:mm:ss" placeholder="Ngày bắt đầu"
              type="datetime" class="w-100 mb-0" :class="{ 'is-invalid': errors.length > 0 }" />
          </ValidationProvider>

        </div>

        <div class="form-group col-lg-3">

          <ValidationProvider class="form-group" rules="required" v-slot="{ errors }">
            <label for="voucher-code">Ngày kết thúc submit thông tin<span class="text-danger pl-1">*</span></label>
            <el-date-picker v-model="entity.end_time" format="dd/MM/yyyy HH:mm:ss" placeholder="Ngày kết thúc"
              type="datetime" class="w-100 mb-0" :class="{ 'is-invalid': errors.length > 0 }" />

          </ValidationProvider>

        </div>

        <div class="form-group col-lg-3">

          <ValidationProvider class="form-group" rules="required" v-slot="{ errors }">
            <label for="voucher-code">Ngày kết thúc duyệt đơn<span class="text-danger pl-1">*</span></label>
            <el-date-picker v-model="entity.end_time_for_admin" format="dd/MM/yyyy HH:mm:ss"
              placeholder="Ngày admin ngừng duyệt đơn" type="datetime" class="w-100 mb-0"
              :class="{ 'is-invalid': errors.length > 0 }" />

          </ValidationProvider>

        </div>

        <div class="col-lg-6">

          <div class="row">
            <div class="form-group col-lg-3">


              <ValidationProvider class="form-group" rules="required|numeric" v-slot="{ errors }">
                <label>Trạng thái</label>
                <el-select class="w-100" :class="{ 'is-invalid': errors.length > 0 }" clearable v-model="entity.status"
                  placeholder="Trạng thái">
                  <el-option v-for="option in URBOX_STATUS" :key="option.VALUE" :label="option.TEXT"
                    :value="option.VALUE" default="2"></el-option>
                </el-select>
              </ValidationProvider>


            </div>

            <div class="form-group col-lg-3">
              <ValidationProvider class="form-group" rules="required" v-slot="{ errors }">
                <label for="voucher-code">App id<span class="text-danger pl-1">*</span></label>
                <input :class="{ 'is-invalid': errors.length > 0 }" type="text" class="form-control mb-0"
                  id="voucher-code" placeholder="App id" required v-model="entity.app_id">
              </ValidationProvider>

            </div>

            <div class="form-group col-lg-6">
              <ValidationProvider class="form-group" v-slot="{ errors }">
                <label for="voucher-code">Email đầu mối LG</label>
                <input :class="{ 'is-invalid': errors.length > 0 }" type="text" class="form-control mb-0"
                  id="voucher-code" placeholder="Email" v-model="entity.email">
              </ValidationProvider>

            </div>
          </div>
        </div>

        <div class="form-group col-lg-6">
          <label for="" class="d-block pt-2 pb-1">Thời gian triển khai chương trình<span
              class="text-danger pl-1">*</span></label>
          <div class="row">
            <div class="col-lg-6">
              <el-date-picker v-model="entity.buy_start_date" type="date" format="dd/MM/yyyy" class="w-100 mb-0"
                placeholder="Ngày bắt đầu triển khai">
              </el-date-picker>
            </div>
            <div class="col-lg-6">
              <el-date-picker v-model="entity.buy_end_date" type="date" format="dd/MM/yyyy" class="w-100 mb-0"
                placeholder="Ngày kết thúc triển khai">
              </el-date-picker>
            </div>
          </div>
        </div>

      </div>
      <hr />
      <!-- -------------------------- -->
      <div class="row">
        <div class="form-group col-lg-6">
          <ValidationProvider class="form-group" rules="required" v-slot="{ errors }">
            <label for="name">Tên hiển thị trên landing page<span class="text-danger pl-1">*</span></label>
            <input :class="{ 'is-invalid': errors.length > 0 }" type="text" class="form-control mb-0" id="name"
              placeholder="Tên campaign" required v-model="entity.name">
          </ValidationProvider>

        </div>

        <div class="form-group col-lg-6">

          <ValidationProvider class="form-group" rules="required" v-slot="{ errors }">
            <label for="description">Mô tả<span class="text-danger pl-1">*</span></label>
            <input :class="{ 'is-invalid': errors.length > 0 }" type="text" class="form-control mb-0 editor"
              id="description" placeholder="Mô tả campaign" required v-model="entity.description">
          </ValidationProvider>

        </div>


        <div class="form-group col-lg-6">

          <ValidationProvider class="form-group" v-slot="{ errors }">
            <div class="row">
              <div class="col-6">
                <label for="voucher-code">Ảnh dại diện của campaign<span class="text-danger pl-1">*</span></label>
                <input type="file" accept="image/*" @change="handleThumbnailImage" />

              </div>
              <div class="col-6">
                <span v-if="previewImage(images.thumbnail_image) !== ''">
                  <img :src="previewImage(images.thumbnail_image)" alt="" width="60" height="60" />
                </span>
                <!--preview image-->
              </div>
            </div>
          </ValidationProvider>

        </div>

        <div class="form-group col-lg-6">

          <ValidationProvider class="form-group" v-slot="{ errors }">
            <div class="row">
              <div class="col-6">
                <label for="voucher-code">Ảnh KV desktop<span class="text-danger pl-1">*</span></label>
                <input type="file" accept="image/*" @change="handleKVDesktopImage" />

              </div>
              <div class="col-6">
                <span v-if="previewImage(images.kv_desktop) !== ''">
                  <img :src="previewImage(images.kv_desktop)" alt="" width="60" height="60" />
                </span>
                <!--preview image-->
              </div>
            </div>
          </ValidationProvider>

        </div>


        <div class="form-group col-lg-6">

          <ValidationProvider class="form-group" v-slot="{ errors }">
            <div class="row">
              <div class="col-6">
                <label for="voucher-code" class="">Ảnh KV mobile<span class="text-danger pl-1">*</span></label>
                <input type="file" accept="image/*" @change="handleKVMobileImage" />
              </div>
              <div class="col-6">
                <span v-if="previewImage(images.kv_mobile) !== ''">
                  <img :src="previewImage(images.kv_mobile)" alt="" width="60" height="60" />
                </span>
                <!--preview image-->
              </div>
            </div>
          </ValidationProvider>

        </div>


        <div class="form-group col-lg-6">

          <ValidationProvider class="form-group" v-slot="{ errors }">


            <div class="row">
              <div class="col-6">
                <label for="voucher-code">Ảnh quà khuyến mại<span class="text-danger pl-1">*</span></label>
                <input type="file" accept="image/*" @change="handleGiftImage" />
              </div>
              <div class="col-6">
                <span v-if="previewImage(images.gift_image) !== ''">
                  <img :src="previewImage(images.gift_image)" alt="" width="60" height="60" />
                </span>
                <!--preview image-->
              </div>
            </div>

          </ValidationProvider>

        </div>

        <div class="form-group col-lg-6">

          <ValidationProvider class="form-group" v-slot="{ errors }">


            <div class="row">
              <div class="col-6">
                <label for="voucher-code">Ảnh mẫu hóa đơn<span class="text-danger pl-1">*</span></label>
                <input type="file" accept="image/*" @change="handleInvoiceImage" />
              </div>
              <div class="col-6">
                <span v-if="previewImage(images.invoice_image) !== ''">
                  <img :src="previewImage(images.invoice_image)" alt="" width="60" height="60" />
                </span>
                <!--preview image-->
              </div>
            </div>

          </ValidationProvider>

        </div>


        <div class="form-group col-lg-6">
          <ValidationProvider class="form-group" v-slot="{ errors }">


            <div class="row">
              <div class="col-6">
                <label for="voucher-code">Ảnh gift card UB</label>
                <input type="file" accept="image/*" @change="handleGiftCardImage" />
              </div>
              <div class="col-6">
                <span v-if="previewImage(images.gift_card_image) !== ''">
                  <img :src="previewImage(images.gift_card_image)" alt="" width="60" height="60" />
                </span>
                <!--preview image-->
              </div>
            </div>

          </ValidationProvider>
        </div>

        <div class="form-group col-lg-6">


          <ValidationProvider class="form-group" v-slot="{ errors }">


            <div class="row">
              <div class="col-6">
                <label for="voucher-code">Ảnh hướng dẫn chụp sản phẩm</label>
                <input max="5" type="file" multiple accept="image/*" @change="handleGuideBannersImage" />
              </div>
              <div class="col-6">
                <span v-if="previewImages(images.guide_banner_image).length">
                  <span class="mr-1" v-for="(imgSrc, index) in previewImages(images.guide_banner_image)" :key="index">
                    <img :src="imgSrc" alt="" width="60" height="60" />
                  </span>
                </span>
                <!--preview image-->
              </div>
            </div>

          </ValidationProvider>

        </div>


        <!--        <div class="form-group col-lg-12">-->

        <!--          <ValidationProvider class="form-group" v-slot="{ errors }">-->
        <!--            <label for="tnc">TNC<span class="text-danger pl-1">*</span></label>-->
        <!--            <textarea :class="{'is-invalid': errors.length > 0}" type="text" class="form-control mb-0"-->
        <!--                      id="tnc" placeholder="TNC của campaign" required-->
        <!--                      v-model="entity.tnc" />-->

        <!--          </ValidationProvider>-->

        <!--        </div>-->

      </div>
      <hr />
      <div class="mb-2 text-bold">
        <label for="">Meta data</label>
      </div>
      <div class="row">
        <div class="form-group col-lg-3">

          <el-switch active-value="2" inactive-value="1" v-model="entity.has_store" active-color="#13ce66"
            inactive-color="#ff4949" active-text="Có sử dụng nhận quà theo store khác nhau">
          </el-switch>
        </div>

        <div class="form-group col-lg-3">
          <el-switch active-value="2" inactive-value="1" v-model="entity.physical_gift" active-color="#13ce66"
            inactive-color="#ff4949" active-text="Có sử dụng quà vật lý">
          </el-switch>
        </div>
        <div class="form-group col-lg-3">
          <el-switch active-value="2" inactive-value="1" v-model="entity.show_urbox_card" active-color="#13ce66"
            inactive-color="#ff4949" active-text="Có sử dụng voucher UrBox">
          </el-switch>
        </div>
        <div class="form-group col-lg-3">
          <el-switch active-value="2" inactive-value="1" v-model="entity.show_lg_card" active-color="#13ce66"
            inactive-color="#ff4949" active-text="Có sử dụng quà của LG">
          </el-switch>
        </div>
        <div class="form-group col-lg-3">
          <el-switch v-model="entity.k_plus" active-value="2" inactive-value="1" active-color="#13ce66"
            inactive-color="#ff4949" active-text="Có sử dụng quà của K+">
          </el-switch>
        </div>
        <div class="form-group col-lg-3">
          <el-switch v-model="entity.passport_require" active-color="#13ce66" active-value="2" inactive-value="1"
            inactive-color="#ff4949" active-text="Có yêu cầu nhập Passport/CCCD">
          </el-switch>
        </div>
        <div class="form-group col-lg-3">
          <el-switch active-value="2" inactive-value="1" v-model="entity.is_combo" active-color="#13ce66"
            inactive-color="#ff4949" active-text="Sử dụng combo nhiều model để nhận quà(giống CP47)">
          </el-switch>
        </div>
        <div class="form-group col-lg-3">
          <el-switch active-value="2" inactive-value="1" v-model="entity.second_phone" active-color="#13ce66"
            inactive-color="#ff4949" active-text="Sử dụng số điện thoại phụ">
          </el-switch>
        </div>
        <div class="form-group col-lg-12" v-if="entity.is_combo === '2'">
          <label for="">Message lỗi khi không đủ điều kiện nhận quà combo</label>
          <input type="text" class="form-control mb-0" id="error_message"
            placeholder="Message lỗi khi không đủ điều kiện nhận quà combo" v-model="entity.error_message">
        </div>
      </div>
      <hr />
      <div class="row">


        <div class="form-group col-lg-4">
          <div>
            <label class="form-label"></label>
          </div>
          <el-button v-if="$permissions.hasAnyPermission(['campaigns.create', 'campaigns.update'])" type="primary"
            native-type="submit">
            Lưu
          </el-button>
        </div>

      </div>
    </ValidationObserver>
  </div>
</template>

<script>
import {
  computed,
  defineComponent,
  useContext,
  ref,
  useRouter,
  useFetch,
  reactive,
  watch,
} from '@nuxtjs/composition-api';
import { Alert, Button, Form, FormItem, Option, Select, Switch, Table, TableColumn, Upload } from 'element-ui';
import { URBOX_STATUS } from '~/util/constant';
import { useCampaign } from '~/composition';

export default defineComponent({
  name: 'CampaignForm',
  layout: 'DashboardLayout',
  props: ['entity', 'errorMessage', 'successMessage'],
  components: {
    [Form.name]: Form,
    [FormItem.name]: FormItem,
    [Option.name]: Option,
    [Select.name]: Select,
    [Button.name]: Button,
    [Alert.name]: Alert,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Upload.name]: Upload,
    [Switch.name]: Switch,
  },
  setup(props) {
    const { store, params } = useContext();
    const router = useRouter();
    const { entity } = props;
    const giftForm = ref(null);
    const images = reactive({
      thumbnail_image: entity.thumbnail_image,
      kv_mobile: entity.kv_mobile,
      kv_desktop: entity.kv_desktop,
      gift_image: entity.gift_image,
      invoice_image: entity.invoice_image,
      gift_card_image: entity.gift_card_image,
      guide_banner_image: entity.guide_banner_image
    });
    watch(entity, () => {
      images.thumbnail_image = entity.thumbnail_image;
      images.kv_mobile = entity.kv_mobile;
      images.kv_desktop = entity.kv_desktop;
      images.gift_image = entity.gift_image;
      images.invoice_image = entity.invoice_image;
      images.gift_card_image = entity.gift_card_image;
      images.guide_banner_image = entity.guide_banner_image;
      if (entity.description.length === 0) {
        entity.description = "Tặng hàng hóa, cung ứng dịch vụ không thu tiền có kèm theo việc mua bán hàng hóa, cung ứng dịch vụ";
      }
    });

    const { createCampaign, saveCampaign } = useCampaign();

    async function handleSubmit() {
      try {
        await store.dispatch('setLoading', true);
        // Gán các file từ images vào entity trước khi submit
        entity.thumbnail_image_img = images.thumbnail_image;
        entity.kv_desktop_img = images.kv_desktop;
        entity.kv_mobile_img = images.kv_mobile;
        entity.gift_image_img = images.gift_image;
        entity.invoice_image_img = images.invoice_image;
        entity.gift_card_image_img = images.gift_card_image;
        entity.guide_banner_img = images.guide_banner_image;
        giftForm.value.validate().then(async (res) => {
          if (res) {
            if (entity.id <= 0) {
              await createCampaign(entity).then(res => {
                router.push('/campaign');
              });
            } else {

              entity.meta_data = {
                buy_start_date: new Date(entity.buy_start_date).toLocaleDateString('vi'),
                buy_end_date: new Date(entity.buy_end_date).toLocaleDateString('vi'),
                has_store: entity.has_store,
                passport_require: entity.passport_require,
                physical_gift: entity.physical_gift,
                k_plus: entity.k_plus,
                show_lg_card: entity.show_lg_card,
                show_urbox_card: entity.show_urbox_card,
                is_combo: entity.is_combo,
                second_phone: entity.second_phone,
                error_message: entity.error_message,
              };
              await saveCampaign(entity, params?.value?.id || 0).then(res => {
                router.push('/campaign');
              });
            }

          }
        });
      } catch (e) {
        console.log(e.message);
        if (e.data && e.data.message) {
          props.errorMessage.value = e.data.message;
        }
      } finally {
        await store.dispatch('setLoading', false);
      }
    }

    const handleThumbnailImage = (event) => {
      images.thumbnail_image = event.target.files[0];
    };

    const handleKVDesktopImage = (event) => {
      images.kv_desktop = event.target.files[0];
    };

    const handleKVMobileImage = (event) => {
      images.kv_mobile = event.target.files[0];
    };
    const handleGiftImage = (event) => {
      images.gift_image = event.target.files[0];
    };
    const handleInvoiceImage = (event) => {
      images.invoice_image = event.target.files[0];
    };
    const handleGiftCardImage = (event) => {
      images.gift_card_image = event.target.files[0];
    };
    const handleGuideBannersImage = (event) => {
      const files = event.target.files;
      images.guide_banner_image = files && files.length > 0 ? Array.from(files) : [];
    };

    const previewImage = (image) => {
      if (!image) return '';
      if (typeof image === 'string') return image;
      if (image instanceof File) return URL.createObjectURL(image);
      return '';
    };
    const previewImages = (images) => {
      if (!images) return [];
      if (typeof images === 'string') return [images];
      if (Array.isArray(images)) {
        return images.map(image => (typeof image === 'string' ? image : (image instanceof File ? URL.createObjectURL(image) : '')));
      }
      return [];
    };
    return {
      handleSubmit,
      entity,
      URBOX_STATUS,
      giftForm,
      handleGiftImage,
      handleThumbnailImage,
      handleKVDesktopImage,
      handleKVMobileImage,
      handleInvoiceImage,
      handleGiftCardImage,
      handleGuideBannersImage,
      previewImage,
      previewImages,
      images,
    };
  },
});
</script>

<style scoped></style>

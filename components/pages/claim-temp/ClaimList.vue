<template>
  <div class="card" v-if="claimState.items">
    <div class="border-0 card-header d-flex justify-content-between">
      <h3 class="mb-0"><PERSON>h sách đơn hàng tạm</h3>
      <ExportClaim :params="params" />
    </div>

    <el-table class="table-responsive" border size="mini" :highlight-current-row="true"
      :cell-style="{ textAlign: 'center' }" :header-cell-style="{ justifyContent: 'center', fontWeight: 'bold' }"
      :data="claimState.items" style="width: 100%">
      <el-table-column type="expand">
        <template slot-scope="{ row }">
          <div style="padding: 1rem 1.5rem">
            <p>
              <strong>Tên khách hàng:</strong>
              {{ row.customer ? row.customer.name : '' }}
            </p>
            <p>
              <strong>Số điện thoại:</strong>
              {{ row.customer.phone | formatPhone }}
            </p>
            <p>
              <strong>Email:</strong>
              {{ row.customer ? row.customer.email : '' }}
            </p>
            <p>
              <strong>Ngày mua:</strong>
              {{ row.orderDate | formatDateFromInt }}
            </p>
            <p>
              <strong>Mã đơn hàng:</strong>
              {{ row.orderCode }}
            </p>
            <p v-if="campainWithKplus">
              <strong>Active K+:</strong>
              {{ row.activeKPlus ? row.activeKPlus : '' }}
            </p>
            <p>
              <strong>Địa điểm mua:</strong>
              {{ row.store ? row.store.address : '' }}
            </p>
            <p>
              <strong>Ngày tạo đơn:</strong>
              {{ row.created | formatDateFromInt }}
            </p>
            <p class="mb-0">
              <strong>Trạng thái:</strong>
              <span v-if="row.process === claimProcess.PENDING.VALUE" class="badge text-wrap badge-info mb-1 lh-120">
                {{ claimProcess.PENDING.TEXT }}
              </span>
              <span v-if="row.process === claimProcess.SUCCESS.VALUE" class="badge text-wrap badge-success mb-1 lh-120">
                {{ claimProcess.SUCCESS.TEXT }}
              </span>
              <span v-if="row.process === claimProcess.IN_PROCESS.VALUE"
                class="badge text-wrap badge-danger mb-1 lh-120">
                {{ claimProcess.IN_PROCESS.TEXT }}
              </span>
            </p>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        v-if="$permissions.hasAnyPermission(['claims.read', 'claims.create', 'claims.update', 'claims.approve'])"
        label="Thao tác" width="90px" class-name="px-1">
        <template v-slot="{ row }">
          <!--          <div class="w-100 d-flex" style="padding: 0 12px">-->
          <el-tooltip content="Xem chi tiết" placement="top" class="mr-1">
            <NuxtLink :to="'/claims-temp/' + row.id">
              <el-button circle size="small" type="primary" icon="el-icon-edit"></el-button>
            </NuxtLink>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="Tên khách hàng" min-width="180px">
        <template v-slot="{ row }">
          {{ row.customer ? row.customer.name : '' }}
        </template>
      </el-table-column>
      <el-table-column label="Số điện thoại" min-width="160px">
        <template v-slot="{ row }">
          {{ row.customer.phone | formatPhone }}
        </template>
      </el-table-column>
      <el-table-column label="Email" min-width="180px">
        <template v-slot="{ row }">
          {{ row.customer ? row.customer.email : '' }}
        </template>
      </el-table-column>
      <el-table-column label="Ngày mua" min-width="180px">
        <template v-slot="{ row }">
          {{ row.orderDate | formatDateFromInt }}
        </template>
      </el-table-column>
      <el-table-column label="Active K+" min-width="180px" v-if="campainWithKplus">
        <template v-slot="{ row }">
          {{ row?.activeKPlus === 2 ? 'Có' : 'Không' }}
        </template>
      </el-table-column>
      <el-table-column label="Trạng thái" min-width="160px">
        <template v-slot="{ row }">
          <span v-if="row.process === claimProcess.PENDING.VALUE" class="badge text-wrap badge-info mb-1 lh-120">
            {{ claimProcess.PENDING.TEXT }}
          </span>
          <span v-if="row.process === claimProcess.SUCCESS.VALUE" class="badge text-wrap badge-success mb-1 lh-120">
            {{ claimProcess.SUCCESS.TEXT }}
          </span>
          <span v-if="row.process === claimProcess.IN_PROCESS.VALUE" class="badge text-wrap badge-danger mb-1 lh-120">
            {{ claimProcess.IN_PROCESS.TEXT }}
          </span>

        </template>
      </el-table-column>
      <el-table-column label="Ngày tạo đơn" min-width="180px">
        <template v-slot="{ row }">
          {{ row.created | formatDatetimeFromInt }}
        </template>
      </el-table-column>
      <el-table-column label="Action" min-width="80px" v-if="$permissions.hasPermission(['claims.delete'])">
        <template v-slot="{ row }">
          <el-tooltip content="Xóa" placement="top" class="mr-1">
            <el-button circle size="small" type="danger" icon="el-icon-delete"
              @click="deleteItem(row.id, campaignId)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!--CARD:FOOTER-->
    <div class="card-footer py-4 d-flex justify-content-end">
      <base-pagination v-if="claimState.items" v-model="params.page" :total="claimState.total"
        :per-page="params.limit"></base-pagination>
    </div>
  </div>
</template>

<script>
import { computed, defineComponent, ref, useContext, useFetch, watch } from '@nuxtjs/composition-api';
import { Table, TableColumn, Button, Message } from 'element-ui';
import { useClaimsTemp } from '@/composition';
import { CLAIM_TEMP_PROCESS } from '@/util/constant.js';
import ExportClaim from '@/components/pages/claim-temp/ExportClaim.vue';
import ConfigCampaign from '@/configs/campaign.json';

export default defineComponent({
  name: 'ClaimList',
  components: {
    ExportClaim,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Button.name]: Button,
  },
  props: {
    params: Object,
  },
  setup(props) {
    const { store } = useContext();
    const { fetchClaims } = useClaimsTemp();
    const { id: campaignId } = store.getters['campaignStore/currentCampaign'];

    let campainWithKplus;
    try {
      campainWithKplus = ConfigCampaign[campaignId]['active_kplus'] === "true";
    } catch {
      campainWithKplus = ConfigCampaign['0']['active_kplus'] === "true";
    }

    async function fetchData() {
      await store.dispatch('setLoading', true);
      await fetchClaims(props.params);
      await store.dispatch('setLoading', false);
    }


    watch(
      () => props.params.page,
      async () => {
        await fetchData();
      },
    );

    const claimState = computed(() => {
      return store.getters['claimTemp/lists'];
    });

    useFetch(async () => {
      await fetchData();
    });

    async function deleteItem(itemId, campaignId) {
      try {
        await store.dispatch('setLoading', true);
        const response = await this.$axios.delete(`temp/claim/${itemId}?campaign_id=${campaignId}`, {
          claim_detail_id: itemId,
          status: -2
        });
        if (response.code === 200) {
          Message({
            message: "Xóa thành công",
            type: 'success',
            duration: 5 * 1000,
          });
        }
        await fetchData();
      } catch (e) {
        Message({
          message: "Có lỗi khi xóa",
          type: 'error',
          duration: 5 * 1000,
        });
      } finally {
        await store.dispatch('setLoading', false);
      }
    }


    return {
      claimState,
      fetchClaims,
      claimProcess: CLAIM_TEMP_PROCESS,
      campaignId,
      campainWithKplus,
      deleteItem,
    };
  },
});
</script>

<style lang="scss">
.el-table .cell {
  justify-content: center;
}

.el-table__cell:first-child {
  padding: unset !important;

  .cell {
    padding-left: unset !important;
  }
}
</style>

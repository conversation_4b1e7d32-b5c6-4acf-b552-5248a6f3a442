<template>
  <div>
    <el-button @click="dialogVisible = !dialogVisible" v-if="$permissions.hasPermission('claims.export')" type="primary"
      icon="el-icon-download" size="mini">
      Xuất file excel
    </el-button>
    <ValidationObserver v-slot="{ invalid }">
      <el-dialog class="dialog-export" title="Xuất file excel" :visible.sync="dialogVisible"
        :before-close="handleBeforeCloseDialog">
        <div class="row">
          <ValidationProvider name="Ngày bắt đầu và kết thúc" rules="required|lt7day" v-slot="{ errors }">
            <div class="col">
              <label>
                Chọn ngày bắt đầu và kết thúc
                <b class="text-danger">*</b>
              </label>
              <el-date-picker class="w-100" v-model="dateRange" type="daterange" range-separator="đến"
                start-placeholder="Từ ngày" end-placeholder="Ngày" format="dd/MM/yyyy"
                :default-time="['00:00:00', '23:59:59']"></el-date-picker>
              <span class="text-danger" v-if="errors.length">{{ errors[0] }}</span>
            </div>
          </ValidationProvider>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="dialogVisible = false">Đóng</el-button>
          <el-button :loading="isLoading" :disabled="invalid" size="small" type="primary"
            @click="handleExportClaimClick">
            Xuất file
          </el-button>
        </span>
      </el-dialog>
    </ValidationObserver>
    <vue-excel-xlsx ref="exportExcel" id="exportExcel" :data="exportData" :columns="columns"
      style="padding: unset; border: unset; display: none" type="xslx" :filename="filename"
      :sheetname="sheetname"></vue-excel-xlsx>
  </div>
</template>

<script>
import { EXPORT_CLAIM_COLUMN } from '@/util/constant.js';
import moment from 'moment';
import { extend } from 'vee-validate';
import httpStatus from 'http-status';
import { Message } from 'element-ui';
import { convertExportClaimData } from '@/util/functions.js';

extend('lt7day', {
  validate: (value) => {
    return moment(value[1]).diff(moment(value[0]), 'days') <= 7;
  },
  message: 'Ngày bắt đầu và kết thúc không được vượt quá 7 ngày.',
});

export default {
  name: 'ExportClaim',
  props: ['params'],
  data() {
    return {
      filename: '',
      sheetname: 'claimData',
      dialogVisible: false,
      isLoading: false,
      startDate: null,
      endDate: null,
      dateRange: null,
      exportData: [],
      columns: EXPORT_CLAIM_COLUMN,
    };
  },
  watch: {
    dateRange: function (value) {
      if (this.dateRange && this.dateRange.length) {
        const from = moment(this.dateRange[0]);
        const to = moment(this.dateRange[1]);

        this.startDate = from.valueOf() / 1000;
        this.endDate = to.valueOf() / 1000;

        this.filename = `claims_${from.format('DD/MM/YYYY')}_${to.format('DD/MM/YYYY')}`;
      } else {
        this.startDate = null;
        this.endDate = null;
      }
    },
  },
  methods: {
    handleBeforeCloseDialog() {
    },
    async handleExportClaimClick() {
      try {
        this.isLoading = true;
        const { data } = await this.$axios.get('temp/claims/export', {
          params: {
            ...this.params,
            start_date: this.startDate,
            end_date: this.endDate,
          },
        });
        this.exportData = convertExportClaimData(data);
        this.$nextTick(() => {
          document.getElementById('exportExcel').click();
        });
        this.dialogVisible = false;
        this.dateRange = null;
        this.startDate = null;
        this.endDate = null;
        this.isLoading = false;
      } catch (e) {
        console.log(e.message, e.lineNumber, e.stack);
        if (e.data.error.code === httpStatus.NOT_FOUND) {
          Message({
            message: 'Không tìm thấy dữ liệu',
            duration: 5000,
            type: 'warning',
          });
        }
        this.isLoading = false;
      }
    },
  },
};
</script>

<style>
.el-dialog {
  margin-top: 15vh;
  width: 100%;
  padding: 0;
}

@media screen and (min-width: 768px) {
  .el-dialog {
    width: 70%;
  }
}

@media screen and (min-width: 966px) {
  .el-dialog {
    width: 50%;
  }
}

@media screen and (min-width: 1024px) {
  .el-dialog {
    width: 40%;
  }
}

@media screen and (min-width: 1280px) {
  .el-dialog {
    width: 30%;
  }
}
</style>

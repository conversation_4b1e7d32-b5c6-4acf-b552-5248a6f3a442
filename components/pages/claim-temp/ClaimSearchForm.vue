<template>
  <div class="row pt-4">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title mb-0">T<PERSON><PERSON> kiếm</h3>
        </div>

        <div class="card-body">
          <form>
            <div class="row">
              <div class="col-lg-3 col-md-6 mb-4">
                <el-input v-model="params.name" placeholder="Tên khách hàng"></el-input>
              </div>

              <div class="col-lg-3 col-md-6 mb-4">
                <el-input type="number" v-model="params.phone" placeholder="Số điện thoại"></el-input>
              </div>

              <div class="col-lg-3 col-md-6 mb-4">
                <el-input v-model="params.email" placeholder="Email"></el-input>
              </div>

              <div class="col-lg-3 col-md-6 mb-4">
                <el-input v-model="params.model" placeholder="Model"></el-input>
              </div>

              <div class="col-lg-3 col-md-6 mb-4">
                <el-input v-model="params.serial" placeholder="Serial"></el-input>
              </div>

              <div class="col-lg-3 col-md-6 mb-4">
                <el-select
                  class="w-100"
                  @change="handleProcessChange"
                  clearable
                  v-model="params.process"
                  placeholder="Trạng thái"
                >
                  <el-option
                    v-for="option in claimProcessType"
                    :key="option.VALUE"
                    :label="option.TEXT"
                    :value="option.VALUE"
                  ></el-option>
                </el-select>
              </div>

              <div class="col-lg-6 col-md-6 mb-4">
                <el-date-picker
                  class="w-100"
                  v-model="dateRange"
                  type="daterange"
                  range-separator="To"
                  start-placeholder="Start date"
                  end-placeholder="End date"
                  format="dd/MM/yyyy"
                  :default-time="['00:00:00', '23:59:59']"
                ></el-date-picker>
              </div>


              <div class="col-lg-12 d-flex justify-content-end">
                <el-button class="col-lg-2 col-md-2 col-sm-12" type="primary" icon="el-icon-search" @click="onSearch()">
                  Tìm kiếm
                </el-button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {computed, defineComponent, ref, useAsync, useContext, watch} from '@nuxtjs/composition-api';
import {CLAIM_TEMP_PROCESS_LIST} from '@/util/constant.js';
  import { Select, Option, DatePicker, Button } from 'element-ui';
  import useClaimsTemp from '@/composition/useClaimsTemp.js';
  import moment from 'moment';
  import useStores from '@/composition/useStores.js';
  import { convertToVietnamesePhone } from '~/util/functions';

  export default defineComponent({
    name: 'ClaimSearchForm',
    props: {
      params: Object,
    },
    components: {
      [Select.name]: Select,
      [Option.name]: Option,
      [DatePicker.name]: DatePicker,
      [Button.name]: Button,
    },
    setup(props) {
      // const store = useStore();
      const { store } = useContext();
      const isLoading = ref(false);
      const { fetchClaims } = useClaimsTemp();
      const { fetchStores } = useStores();

      const dateRange = ref(null);

      useAsync(async () => {
        await fetchStores({});
      });

      const stores = computed(() => {
        return store.getters['store/items'];
      });

      watch(dateRange, () => {
        if (dateRange.value && dateRange.value.length) {
          props.params.startDate = moment(dateRange.value[0]).valueOf() / 1000;
          props.params.endDate = moment(dateRange.value[1]).valueOf() / 1000;
        } else {
          props.params.startDate = null;
          props.params.endDate = null;
        }
      });

      const onSearch = async () => {
        await store.dispatch('setLoading', true);
        props.params.phone = parseInt(convertToVietnamesePhone(props.params.phone), 0) || null;
        props.params.page = 1;
        props.params.name = props.params.name.trim();
        props.params.email = props.params.email.trim();
        props.params.model = props.params.model.trim();
        props.params.serial = props.params.serial.trim();
        await fetchClaims(props.params);
        await store.dispatch('setLoading', false);
      };
      const handleProcessChange = (input) => {
        if (!input) {
          props.params.process = null;
        }
      };




      return {
        claimProcessType: CLAIM_TEMP_PROCESS_LIST,
        onSearch,
        dateRange,
        stores,
        isLoading,
        handleProcessChange,
      };
    },
  });
</script>

<style lang="scss">
  .el-select .el-input .el-input__inner {
    height: 40px;
  }

  .el-date-editor {
    .el-range-separator {
      width: 10% !important;
    }
    .el-icon-date {
      margin-left: 0;
    }
    i {
      width: 8% !important;
    }
  }
</style>

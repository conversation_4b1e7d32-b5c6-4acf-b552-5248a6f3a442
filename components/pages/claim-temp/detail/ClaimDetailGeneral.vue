<template>
  <div class="col-lg-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title mb-0">Thông tin chung</h3>
      </div>
      <div class="card-body d-flex justify-content-between">
        <div class="row">
          <div class="col-lg-9 col-md-6">
            <div class="row">
              <div class="form-group col-lg-4">
                <label class="form-label">Tên khách hàng</label>
                <el-input label="Tên khách hàng" placeholder="Nhập tên khách hàng"
                  v-model="customerInfo.name" class="el-form-item mb-0"></el-input>
              </div>

              <div class="form-group col-lg-4">
                <label class="form-label">Số điện thoại</label>
                <el-input label="Tên khách hàng" placeholder="Nhập số điện thoại"
                  v-model="customerInfo.phone" class="el-form-item mb-0"></el-input>
              </div>

              <div class="form-group col-lg-4">
                <label class="form-label">CMND/CCCD</label>
                <el-input  label="CMND/CCCD" placeholder="CMND/CCCD"
                  v-model="customerInfo.identification"></el-input>
              </div>

              <div class="form-group col-lg-4">
                <label class="form-label">Email</label>
                <el-input  placeholder="Nhập email" v-model="customerInfo.email"></el-input>
              </div>
              <div class="form-group col-lg-4">
                <label class="form-label">Ngày mua</label>
                <el-date-picker disabled v-model="customerInfo.orderDate" format="dd/MM/yyyy" placeholder="Ngày mua hàng"
                  type="date" class="w-100 el-form-item mb-0"></el-date-picker>
              </div>
              <div class="form-group col-lg-4">
                <label class="form-label">Xác nhận tên công ty/ tên KH trên hóa đơn</label>
                <el-input type="textarea" rows="2"  placeholder="Tên công ty"
                  v-model="customerInfo.companyName"></el-input>
              </div>

              <div class="form-group col-lg-4">
                <label class="form-label">Số điện thoại phụ</label>
                <el-input rows="2"
                  v-model="customerInfo.phoneSecond"></el-input>
              </div>


              <div class="form-group col-lg-4" v-if="campainWithKplus">
                <label class="form-label">
                  Active K+
                  <span :class="activeKplus ? 'text-green' : 'text-red'">{{ activeKplus ? 'CÓ' : 'KHÔNG' }}</span>
                </label>
              </div>

              <div class="form-group col-lg-4" >
                <label class="form-label">&nbsp;
                  </label>
                <el-button v-if="$permissions.hasAnyPermission(['claims.create','claims.update'])"
                           class="col-sm-12" type="primary" icon="el-icon-download" @click="saveCustomerTemp">
                  Lưu thông tin
                </el-button>
              </div>

            </div>
          </div>
          <div class="col-lg-3 col-md-6">
            <label class="form-label">Ảnh hoá đơn</label>
            <img style="width: 100%" :src="`${$config.baseImageUrl}${claim.orderImage}` || '/img/mockup.png'"
              alt="Ảnh hoá đơn" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, useContext } from '@nuxtjs/composition-api';
import { Alert } from 'element-ui';
import ConfigCampaign from '@/configs/campaign.json';
import { useClaims } from '~/composition';

export default defineComponent({
  name: 'ClaimDetailGeneral',
  props: {
    claim: Object,
    user: Object,
    initData: Function,
  },
  components: {
    [Alert.name]: Alert,
  },
  setup(props) {

    const { saveCustomerTempInfo } = useClaims();
    // const store = useStore();
    const { store } = useContext();
    const { id: campaignId } = store.getters['campaignStore/currentCampaign'];
    let campainWithKplus;
    try {
      campainWithKplus = ConfigCampaign[campaignId]['active_kplus'] === 'true';
    } catch {
      campainWithKplus = ConfigCampaign['0']['active_kplus'] === 'true';
    }

    const activeKplus = ref(props.claim.activeKPlus === 2);
    const errorMessage = ref('');
    const successMessage = ref('');
    const customerInfo = ref({
      name: props.claim.customer.name,
      identification: props.claim.customer.identification,
      phone: props.claim.customer.phone,
      email: props.claim.customer.email,
      orderDate: props.claim.orderDate * 1000,
      companyName: props.claim.companyName,
      phoneSecond: props.claim.customer.phoneSecond,
    });
    const saveCustomerTemp = async () => {
      try {
        errorMessage.value = '';
        successMessage.value = '';
        await store.dispatch('setLoading', true);
        customerInfo.value.orderDate = Math.floor(new Date(customerInfo.value.orderDate).getTime() / 1000);
        const result = await saveCustomerTempInfo(props.claim.id, {
          ...customerInfo.value,
          campaign_id: campaignId,
          adminId: props.user.id,
        });
        if (result.success) {
          successMessage.value = 'Customer info saved';
          window.location.reload();
        } else {
          errorMessage.value = result.msg;
        }
      } catch (e) {
        if (e.data && e.data.message) {
          errorMessage.value = e.data.message;
        }
      } finally {
        await store.dispatch('setLoading', false);
      }
    }

    return {
      campaignId,
      activeKplus,
      campainWithKplus,
      customerInfo,
      errorMessage,
      successMessage,
      saveCustomerTemp
    };
  },
});
</script>

<style>
.active-kplus {
  width: 18px !important;
  height: 18px !important;
  padding: 0px !important;
  border-radius: 0px !important;
  margin-left: 10px;
}

.el-form-item.is-success .el-input__inner {
  border-color: #2dce89;
}

.el-input.is-unset .el-input__inner {
  border-color: #e4e7ed !important;
}
</style>

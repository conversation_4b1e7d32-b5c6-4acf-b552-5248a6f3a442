<template>
    <div>
      <el-button @click="dialogVisible = !dialogVisible" type="success" icon="el-icon-document" size="mini">
      </el-button>
      <!-- <ValidationObserver v-slot="{ invalid }"> -->
        <el-dialog
            class="dialog-export"
            title="Lịch sử Gọi"
            :visible.sync="dialogVisible"
            :before-close="handleBeforeCloseDialog"
        >
          <div>
            <el-table class="table-responsive"  :highlight-current-row="true"
              :cell-style="{ textAlign: 'center' }"
              :header-cell-style="{ justifyContent: 'center', fontWeight: 'bold' }" :data="filteredClaimLogs"
              style="width: 100%">
              <el-table-column label="Tài khoản">
                <template v-slot="{ row }">
                  {{ row.account }}
                </template>
              </el-table-column>
              <el-table-column label="Ngày tạo">
                <template v-slot="{ row }">
                  {{ row.created_at | formatDatetimeFromInt }}
                </template>
              </el-table-column>
              <el-table-column label="Hành động">
                <template v-slot="{ row }">
                  <div v-if="row.type === 1">Gọi lần 1</div>
                  <div v-else-if="row.type === 2">Gọi lần 2</div>
                  <div v-else-if="row.type === 3">Gọi lần 3</div>
                  <div v-else-if="row.type === 4">SĐT trên hóa đơn/ SĐT Kích hoạt bảo hành </div>
                </template>
              </el-table-column>
              <el-table-column label="Ghi chú">
                <template v-slot="{ row }">
                  {{ row.note }}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button size="small" @click="dialogVisible = false">Đóng</el-button>
          </span>
        </el-dialog>
      <!-- </ValidationObserver> -->
    </div>
  </template>
  
  <script>
  import {computed, ref, useContext} from "@nuxtjs/composition-api";
  // import {Message} from "element-ui";
  import {useClaims} from "~/composition";
  // import {integer} from "vee-validate/dist/rules";
  import { Alert, Button, Form, FormItem, Message, Option, Select, Table, TableColumn } from 'element-ui';
  export default {
    name: "ClaimDetailLogs",
    props: {
      claimLogs: Array,
    },
    components: {
      [Form.name]: Form,
      [FormItem.name]: FormItem,
      [Option.name]: Option,
      [Select.name]: Select,
      [Button.name]: Button,
      [Alert.name]: Alert,
      [Table.name]: Table,
      [TableColumn.name]: TableColumn,
    },
    computed: {
      filteredClaimLogs() {
      // Lọc dữ liệu chỉ giữ lại những hàng có type không xác định hoặc nhỏ hơn 1
      return this.claimLogs.filter(row => row.type || row.type >= 1).sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
      // return this.claimLogs
      //   .filter(row => row.type || row.type >= 1)
      //   .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
    }
    },
    setup(props) {
      const {store} = useContext();
      const isLoading = ref(false);
      const dialogVisible = ref(false);
      const currentCampaign = store.getters['campaignStore/currentCampaign'];
      const user = computed(() => {
        return store.getters['loggedInUser'];
      });
      const {importExcelApprove} = useClaims();
      // user.value.email/id;
      // var approveFile = null;
      const handleBeforeCloseDialog = () => {}
      
      return {
        isLoading,
        dialogVisible,
        handleBeforeCloseDialog
      }
    }
  
  }
  </script>
  
  <style>
  @media screen and (min-width: 768px) {
    .el-dialog {
      width: 70%;
    }
  }

  @media screen and (min-width: 966px) {
    .el-dialog {
      width: 70%;
    }
  }

  @media screen and (min-width: 1024px) {
    .el-dialog {
      width: 70%;
    }
  }

  @media screen and (min-width: 1280px) {
    .el-dialog {
      width: 70%;
    }
  }
  </style>
  
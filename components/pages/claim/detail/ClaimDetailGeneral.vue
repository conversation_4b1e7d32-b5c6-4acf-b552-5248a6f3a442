<template>
  <div class="col-lg-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title mb-0">Thông tin chung</h3>
      </div>
      <div class="card-body d-flex justify-content-between">
        <div class="row">
          <div class="col-lg-9 col-md-6">
            <div class="row">
              <div class="form-group col-lg-4">
                <label class="form-label">Tên khách hàng</label>
                <el-input :disabled="!!claim.approveTwo" label="Tên khách hàng" placeholder="Nhập tên khách hàng"
                  v-model="customerInfo.name" class="el-form-item mb-0" :class="{
                    'is-unset': !stateClaimDetailGeneral?.hasClaimOcr,
                    'is-error': customerNameTracking() && !stateClaimDetailGeneral.isValidCustomerName,
                    'is-success': customerNameTracking() && stateClaimDetailGeneral.isValidCustomerName,
                  }"></el-input>
                <div class="text-xs ml-1" :class="{
                  'text-danger mt-1': customerNameTracking() && !stateClaimDetailGeneral.isValidCustomerName,
                  'text-success mt-1': customerNameTracking() && stateClaimDetailGeneral.isValidCustomerName,
                }" v-if="customerNameTracking()">
                  <el-button circle type="primary" icon="el-icon-caret-bottom" size="mini" v-if="
                    (($permissions.canChangeOcr() && customerNameTracking().manual > 0) || !stateClaimDetailGeneral.isValidCustomerName) && currentDropdownOpened !== 'customerNameTracking'
                  " @click="handleShowOcrDropdown('customerNameTracking')" v-loading="loadingState"></el-button>
                  <el-select v-if="currentDropdownOpened === 'customerNameTracking'"
                    @change="(value) => onOcrDropdownChange(value, 'customerNameTracking')" v-model="ocrDropdownValue"
                    v-loading="loadingState" class="mb-1">
                    <el-option v-for="option in ocrDropdownOptions.slice(0, -1)" :key="option.VALUE"
                      :value="option.VALUE" :label="option.TEXT"></el-option>
                    <el-option :key="ocrDropdownOptions[3].VALUE" :value="ocrDropdownOptions[3].VALUE"
                      :label="ocrDropdownOptions[3].TEXT"></el-option>
                  </el-select>
                  <div v-if="noteVisible['customerNameTracking']">
                    <el-input type="textarea" rows="4" v-model="currentOcrNoteValue['customerNameTracking']"
                      placeholder="Nhập ghi chú" class="mb-1"></el-input>
                    <div class="d-flex">
                      <el-button type="primary" @click="handleSubmitOcrNote('customerNameTracking')">
                        Nhập ghi chú
                      </el-button>
                      <el-button type="info" @click="handleCancelNote('customerNameTracking')">
                        Huỷ
                      </el-button>
                    </div>

                  </div>
                  <i>
                    <b>
                      {{ customerNameTracking()?.patternUser || '' }}
                      {{
                        customerNameTracking()?.patternCompany ? `- (${customerNameTracking()?.patternCompany})` : ''
                      }}
                    </b>
                  </i>
                  <i>({{ customerNameTracking()?.message }})</i>
                  <i v-if="customerNameTracking()?.note">Note: {{ customerNameTracking()?.note }}</i>
                </div>
              </div>

              <div class="form-group col-lg-4">
                <label class="form-label">Số điện thoại</label>
                <el-input :disabled="!!claim.approveTwo" label="Tên khách hàng" placeholder="Nhập số điện thoại"
                  v-model="customerInfo.phone" class="el-form-item mb-0" :class="{
                    'is-unset': !stateClaimDetailGeneral?.hasClaimOcr,
                    'is-error': !stateClaimDetailGeneral.isValidCustomerPhone,
                    'is-success': stateClaimDetailGeneral.isValidCustomerPhone,
                  }"></el-input>
                <div class="text-xs ml-1" :class="{
                  'text-danger mt-1': !stateClaimDetailGeneral.isValidCustomerPhone,
                  'text-success mt-1': stateClaimDetailGeneral.isValidCustomerPhone,
                }" v-if="customerPhoneTracking()">
                  <el-button circle type="primary" icon="el-icon-caret-bottom" size="mini" v-if="
                    (($permissions.canChangeOcr() && customerPhoneTracking().manual > 0) || !stateClaimDetailGeneral.isValidCustomerPhone) && currentDropdownOpened !== 'customerPhoneTracking'
                  " @click="handleShowOcrDropdown('customerPhoneTracking')" v-loading="loadingState"></el-button>
                  <el-select v-if="currentDropdownOpened === 'customerPhoneTracking'"
                    @change="(value) => onOcrDropdownChange(value, 'customerPhoneTracking')" v-model="ocrDropdownValue"
                    v-loading="loadingState" class="mb-1">
                    <el-option v-for="option in ocrDropdownOptions.slice(0, -1)" :key="option.VALUE"
                      :value="option.VALUE" :label="option.TEXT"></el-option>
                    <el-option :key="ocrDropdownOptions[3].VALUE" :value="ocrDropdownOptions[3].VALUE"
                      :label="ocrDropdownOptions[3].TEXT"></el-option>
                  </el-select>
                  <div v-if="noteVisible['customerPhoneTracking']">
                    <el-input type="textarea" rows="4" v-model="currentOcrNoteValue['customerPhoneTracking']"
                      placeholder="Nhập ghi chú" class="mb-1"></el-input>
                    <div class="d-flex"><el-button type="primary" @click="handleSubmitOcrNote('customerPhoneTracking')">
                        Nhập ghi chú
                      </el-button>
                      <el-button type="info" @click="handleCancelNote('customerPhoneTracking')">
                        Huỷ
                      </el-button>
                    </div>

                  </div>
                  <i>
                    <b>{{ customerPhoneTracking()?.pattern || '' }}</b>
                    ({{ customerPhoneTracking()?.message || '' }})
                  </i>
                  <i v-if="customerPhoneTracking()?.note">Note: {{ customerPhoneTracking()?.note || '' }}</i>
                </div>
              </div>

              <div class="form-group col-lg-4">
                <label class="form-label">CMND/CCCD</label>
                <el-input :disabled="!!claim.approveTwo" label="CMND/CCCD" placeholder="CMND/CCCD"
                  v-model="customerInfo.identification"></el-input>
              </div>

              <div class="form-group col-lg-4">
                <label class="form-label">Email</label>
                <el-input :disabled="!!claim.approveTwo" placeholder="Nhập email" v-model="customerInfo.email" :class="{
                  'el-form-item is-error mb-0':
                    customerEmailTracking() &&
                    customerEmailTracking()?.result === false &&
                    customerEmailTracking()?.manual !== 2,
                  'el-form-item is-success mb-0':
                    customerEmailTracking() &&
                    (customerEmailTracking()?.result === true || customerEmailTracking()?.manual === 2),
                }"></el-input>
                <div class="text-xs ml-1" :class="{
                  'text-danger mt-1':
                    customerEmailTracking() &&
                    customerEmailTracking()?.result === false &&
                    customerEmailTracking()?.manual !== 2,
                  'text-success mt-1':
                    customerEmailTracking() &&
                    (customerEmailTracking()?.result === true || customerEmailTracking()?.manual === 2),
                }" v-if="customerEmailTracking()">
                  <el-button circle type="primary" icon="el-icon-success" size="mini"
                    v-if="customerEmailTracking()?.manual !== 2 && !customerEmailTracking()?.result"
                    @click="handleUpdateOcrData('customerEmailTracking')"></el-button>
                  <i>
                    <b>{{ customerEmailTracking()?.pattern || '' }}</b>
                    ({{ customerEmailTracking()?.message || '' }})
                  </i>
                </div>
              </div>
              <div class="form-group col-lg-4">
                <label class="form-label">Ngày mua</label>
                <el-date-picker v-model="customerInfo.orderDate" format="dd/MM/yyyy" placeholder="Chọn ngày mua hàng"
                  type="date" class="w-100 el-form-item mb-0" :class="{
                    'is-unset': !stateClaimDetailGeneral?.hasClaimOcr,
                    'is-error': !stateClaimDetailGeneral.isValidInvoiceDate,
                    'is-success': stateClaimDetailGeneral.isValidInvoiceDate,
                  }"></el-date-picker>
                <div class="text-xs ml-1" :class="{
                  'text-danger mt-1': !stateClaimDetailGeneral.isValidInvoiceDate,
                  'text-success mt-1': stateClaimDetailGeneral.isValidInvoiceDate,
                }" v-if="invoiceDateTracking()">
                  <el-button circle type="primary" icon="el-icon-caret-bottom" size="mini" v-if="
                    (($permissions.canChangeOcr() && invoiceDateTracking().manual > 0) || !stateClaimDetailGeneral.isValidInvoiceDate) && currentDropdownOpened !== 'invoiceDateTracking'
                  " @click="handleShowOcrDropdown('invoiceDateTracking')" v-loading="loadingState"></el-button>
                  <el-select v-if="currentDropdownOpened === 'invoiceDateTracking'"
                    @change="(value) => onOcrDropdownChange(value, 'invoiceDateTracking')" v-model="ocrDropdownValue"
                    v-loading="loadingState" class="mb-1">
                    <el-option v-for="option in ocrDropdownOptions.slice(0, -1)" :key="option.VALUE"
                      :value="option.VALUE" :label="option.TEXT"></el-option>
                    <el-option :key="ocrDropdownOptions[3].VALUE" :value="ocrDropdownOptions[3].VALUE"
                      :label="ocrDropdownOptions[3].TEXT"></el-option>
                  </el-select>
                  <div v-if="noteVisible['invoiceDateTracking']">
                    <el-input type="textarea" rows="4" v-model="currentOcrNoteValue['invoiceDateTracking']"
                      placeholder="Nhập ghi chú" class="mb-1"></el-input>
                    <div class="d-flex"><el-button type="primary" @click="handleSubmitOcrNote('invoiceDateTracking')">
                        Nhập ghi chú
                      </el-button>
                      <el-button type="info" @click="handleCancelNote('invoiceDateTracking')">
                        Huỷ
                      </el-button>
                    </div>

                  </div>
                  <i>
                    <b>{{ invoiceDateTracking()?.pattern || '' }}</b>
                    ({{ invoiceDateTracking()?.message || '' }})
                  </i>
                  <i v-if="invoiceDateTracking()?.note">Note: {{ invoiceDateTracking()?.note || '' }}</i>
                </div>
              </div>
              <div class="form-group col-lg-4">
                <label class="form-label">Địa chỉ mua hàng</label>
                <el-input disabled type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" :value="claim.store.address"
                  placeholder="Nhập địa chỉ mua hàng"></el-input>
                <!--                <span class=" text-xs ml-1"-->
                <!--                      :class="{-->
                <!--                            'text-danger': lineItemsTracking() && lineItemsTracking().result === false,-->
                <!--                            'text-success': lineItemsTracking() && lineItemsTracking().result === true,-->
                <!--                      }"-->
                <!--                      v-if="lineItemsTracking()">-->
                <!--                  <i><b>{{ lineItemsTracking()?.pattern || '' }}</b> ({{ lineItemsTracking()?.message || '' }})</i>-->
                <!--                </span>-->
              </div>
              <div class="form-group col-lg-4">
                <label class="form-label">Xác nhận tên công ty/ tên KH trên hóa đơn</label>
                <el-input type="textarea" rows="2" :disabled="!!claim.approveTwo" placeholder="Nhập tên công ty"
                  v-model="customerInfo.companyName"></el-input>
              </div>

              <div class="form-group col-lg-4">
                <label class="form-label">Số điện thoại phụ</label>
                <el-input disabled rows="2" v-model="customerInfo.phoneSecond"></el-input>
              </div>

              <div class="form-group col-lg-12">
                <el-alert center type="success" show-icon closable :title="successMessage" v-if="successMessage"
                  class="mb-4" />
                <el-alert center type="error" show-icon closable :title="errorMessage" v-if="errorMessage"
                  class="mb-4" />
                <el-button v-if="$permissions.hasAnyPermission(['claims.create', 'claims.update'])"
                  class="col-lg-2 col-md-2 col-sm-12" type="primary" icon="el-icon-download" @click="saveCustomer()">
                  Lưu thông tin
                </el-button>
                <hr />
              </div>

              <div class="form-group col-lg-4" v-if="campainWithKplus">
                <label class="form-label">
                  Active K+
                  <span>
                    <el-popconfirm confirmButtonText="Có" cancelButtonText="Hủy" icon="el-icon-info" iconColor="red"
                      :title="`Bạn có chắc chắn muốn ${activeKplus ? 'hủy active' : 'active'}`"
                      @confirm="changeActiveKplus">
                      <el-button slot="reference" class="active-kplus">
                        <i class="el-icon-check" v-show="activeKplus" style="color: green"></i>
                        <i class="el-icon-check" v-show="!activeKplus" style="color: white"></i>
                      </el-button>
                    </el-popconfirm>
                  </span>
                  <span :class="activeKplus ? 'text-green' : 'text-red'">{{ activeKplus ? 'CÓ' : 'KHÔNG' }}</span>
                </label>
              </div>

              <div class="form-group col-lg-12" v-if="sellerTracking()">
                <div class="row">
                  <div class="col-12">
                    <label class="form-label">
                      Tên cửa hàng:
                      <span class="text-md font-weight-normal">{{ sellerTracking().name ?? '-' }}</span>
                    </label>
                  </div>
                  <div class="col-12">
                    <label class="form-label">
                      Mã số thuế:
                      <span class="text-md font-weight-normal">{{ sellerTracking().taxCode ?? '-' }}</span>
                    </label>
                  </div>
                  <div class="col-12">
                    <label class="form-label">
                      Địa chỉ:
                      <span class="text-md font-weight-normal">{{ sellerTracking().address ?? '-' }}</span>
                    </label>
                  </div>
                  <div class="col-12">
                    <label class="form-label">
                      Số điện thoại:
                      <span class="text-md font-weight-normal">{{ sellerTracking().phone ?? '-' }}</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6">
            <label class="form-label">Ảnh hoá đơn</label>
            <img style="width: 100%" :src="`${$config.baseImageUrl}${claim.orderImage}` || '/img/mockup.png'"
              alt="Ảnh hoá đơn" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, useContext, computed } from '@nuxtjs/composition-api';
import { useClaims } from '@/composition';
import { Alert, Message } from 'element-ui';
import { OCR_DROPDOWN_OPTIONS_LIST } from '@/util/constant.js';
import ConfigCampaign from '../../../../configs/campaign.json';

export default defineComponent({
  name: 'ClaimDetailGeneral',
  props: {
    claim: Object,
    user: Object,
    initData: Function,
  },
  components: {
    [Alert.name]: Alert,
  },
  setup(props) {
    const checkCustomerNameValidity = () => {
      const customerNameTracking = props.claim?.claimOcr?.metaData?.customerNameTracking;
      return customerNameTracking?.result === true || customerNameTracking?.manual > 0;
    };
    const checkCustomerPhoneValidity = () => {
      const customerPhoneTracking = props.claim?.claimOcr?.metaData?.customerPhoneTracking;
      return customerPhoneTracking?.result === true || customerPhoneTracking?.manual > 0;
    };
    const checkInvoiceDateValidity = () => {
      const invoiceDateTracking = props.claim?.claimOcr?.metaData?.invoiceDateTracking;
      return invoiceDateTracking?.result === true || invoiceDateTracking?.manual > 0;
    };
    const stateClaimDetailGeneral = computed(() => ({
      hasClaimOcr: props.claim?.claimOcr,
      isValidCustomerName: checkCustomerNameValidity(),
      isValidCustomerPhone: checkCustomerPhoneValidity(),
      isValidInvoiceDate: checkInvoiceDateValidity(),
    }));
    // const store = useStore();
    const { store } = useContext();
    const { id: campaignId } = store.getters['campaignStore/currentCampaign'];
    let campainWithKplus;
    try {
      campainWithKplus = ConfigCampaign[campaignId]['active_kplus'] === 'true';
    } catch {
      campainWithKplus = ConfigCampaign['0']['active_kplus'] === 'true';
    }

    const ocrDropdownOptions = OCR_DROPDOWN_OPTIONS_LIST;

    const { updateActiveKplus, saveCustomerInfo, updateOcrData } = useClaims();
    const loadingState = ref(false);
    const activeKplus = ref(props.claim.activeKPlus === 2);
    const errorMessage = ref('');
    const successMessage = ref('');
    const customerInfo = ref({
      name: props.claim.customer.name,
      identification: props.claim.customer.identification,
      phone: props.claim.customer.phone,
      email: props.claim.customer.email,
      orderDate: props.claim.orderDate * 1000,
      companyName: props.claim.companyName || props.claim?.claimOcr?.metaData?.customerNameTracking?.patternCompany,
      phoneSecond: props.claim.customer.phoneSecond,
    });

    const currentDropdownOpened = ref('');
    const ocrDropdownValue = ref('');
    const noteVisible = ref({
      customerNameTracking: false,
      customerPhoneTracking: false,
      invoiceDateTracking: false,
    });
    const currentOcrNoteValue = ref({
      customerNameTracking: '',
      customerPhoneTracking: '',
      invoiceDateTracking: '',
    });

    const changeActiveKplus = async () => {
      const data = await updateActiveKplus(props.claim.id, {
        campaign_id: campaignId,
        active_k_plus: activeKplus.value ? 1 : 2,
      });
      if (data) {
        Message({
          message: `${activeKplus.value ? 'Hủy ' : 'Active '}thành công`,
          type: 'success',
          duration: 5 * 1000,
        });
        activeKplus.value = !activeKplus.value;
      }
    };

    const saveCustomer = async () => {
      try {
        errorMessage.value = '';
        successMessage.value = '';
        await store.dispatch('setLoading', true);
        customerInfo.value.orderDate = Math.floor(new Date(customerInfo.value.orderDate).getTime() / 1000);
        const result = await saveCustomerInfo(props.claim.id, {
          ...customerInfo.value,
          campaign_id: campaignId,
          adminId: props.user.id,
        });
        if (result.success) {
          successMessage.value = 'Customer info saved';
          window.location.reload();
        } else {
          errorMessage.value = result.msg;
        }
      } catch (e) {
        if (e.data && e.data.message) {
          errorMessage.value = e.data.message;
        }
      } finally {
        await store.dispatch('setLoading', false);
      }
    };

    const customerNameTracking = () => {
      const _customerNameTracking = props.claim?.claimOcr?.metaData?.customerNameTracking || null;
      console.log('_customerNameTracking', _customerNameTracking);
      if (_customerNameTracking) {
        return {
          message: '',
          original: '',
          pattern: '',
          result: undefined,
          manual: 0,
          ..._customerNameTracking,
        };
      }
      return null;
    };
    const customerPhoneTracking = () => {
      const _customerPhoneTracking = props.claim?.claimOcr?.metaData?.customerPhoneTracking || null;
      console.log('_customerPhoneTracking', _customerPhoneTracking);
      if (_customerPhoneTracking) {
        return {
          message: '',
          original: '',
          pattern: '',
          result: undefined,
          manual: 0,
          ..._customerPhoneTracking,
        };
      }
      return null;
    };
    const customerEmailTracking = () => {
      const _customerEmailTracking = props.claim?.claimOcr?.metaData?.customerEmailTracking || null;
      console.log('_customerEmailTracking', _customerEmailTracking);
      if (_customerEmailTracking) {
        return {
          message: '',
          original: '',
          pattern: '',
          result: undefined,
          manual: 1,
          ..._customerEmailTracking,
        };
      }
      return null;
    };

    const sellerTracking = () => {
      const _sellerTracking = props.claim?.claimOcr?.metaData?.sellerTracking || null;
      console.log('_invoiceDateTracking', _sellerTracking);
      if (_sellerTracking) {
        return {
          name: '',
          taxCode: '',
          address: '',
          phone: '',
          ..._sellerTracking,
        };
      }
      return null;
    };
    const invoiceDateTracking = () => {
      const _invoiceDateTracking = props.claim?.claimOcr?.metaData?.invoiceDateTracking || null;
      console.log('_invoiceDateTracking', _invoiceDateTracking);
      if (_invoiceDateTracking) {
        return {
          message: '',
          original: '',
          pattern: '',
          result: undefined,
          manual: 0,
          ..._invoiceDateTracking,
        };
      }
      return null;
    };

    const handleShowOcrDropdown = (field) => {
      currentDropdownOpened.value = field;
      ocrDropdownValue.value = '';
    };

    function onOcrDropdownChange(selectedValue, field) {
      if (selectedValue !== ocrDropdownOptions[3].VALUE) {
        handleUpdateOcrData(field, selectedValue, ocrDropdownOptions.find(option => option.VALUE === selectedValue).TEXT);
        currentDropdownOpened.value = '';
        noteVisible.value[field] = false;
      } else {
        noteVisible.value[field] = true;
      }
    }

    function handleSubmitOcrNote(field) {
      handleUpdateOcrData(field, ocrDropdownOptions[3].VALUE, currentOcrNoteValue.value[field]);
      currentDropdownOpened.value = '';
      noteVisible.value[field] = false;
    }

    function handleCancelNote(field) {
      currentDropdownOpened.value = '';
      noteVisible.value[field] = false;
    }

    const handleUpdateOcrData = async (field, value, note) => {
      loadingState.value = true;
      const res = await updateOcrData({
        claimId: props.claim.id,
        field: field,
        value: value,
        adminId: props.user.id,
        note: note,
      });
      if (res) {
        await props.initData();
      }
      setTimeout(() => {
        loadingState.value = false;
      }, 4000);
    };

    return {
      campaignId,
      updateActiveKplus,
      changeActiveKplus,
      activeKplus,
      campainWithKplus,
      saveCustomer,
      customerInfo,
      errorMessage,
      successMessage,
      customerNameTracking,
      // lineItemsTracking,
      sellerTracking,
      invoiceDateTracking,
      customerPhoneTracking,
      customerEmailTracking,
      handleUpdateOcrData,
      loadingState,
      stateClaimDetailGeneral,
      currentDropdownOpened,
      handleShowOcrDropdown,
      ocrDropdownOptions,
      ocrDropdownValue,
      onOcrDropdownChange,
      handleSubmitOcrNote,
      handleCancelNote,
      noteVisible,
      currentOcrNoteValue,
    };
  },
});
</script>

<style>
.active-kplus {
  width: 18px !important;
  height: 18px !important;
  padding: 0px !important;
  border-radius: 0px !important;
  margin-left: 10px;
}

.el-form-item.is-success .el-input__inner {
  border-color: #2dce89;
}

.el-input.is-unset .el-input__inner {
  border-color: #e4e7ed !important;
}
</style>

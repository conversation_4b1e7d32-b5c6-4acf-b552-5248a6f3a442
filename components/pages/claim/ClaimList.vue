<template>
  <div class="card" v-if="claimState.items">
    <div class="border-0 card-header d-flex justify-content-between">
      <h3 class="mb-0"><PERSON>h sách đơn hàng</h3>
      <ImportClaimApprove v-if="$permissions.hasAnyPermission(['claims.create', 'claims.update'])" :approve-time="1" />
      <ImportClaimApprove v-if="$permissions.hasAnyPermission(['claims.create', 'claims.update'])" :approve-time="2" />
      <ExportClaim :params="params" />
    </div>

    <el-table class="table-responsive" border size="mini" :highlight-current-row="true"
      :cell-style="{ textAlign: 'center' }" :header-cell-style="{ justifyContent: 'center', fontWeight: 'bold' }"
      :data="claimState.items" style="width: 100%">
      <el-table-column type="expand">
        <template slot-scope="{ row }">
          <div class="p-4">
            <div class="row">
              <div class="col-6">
                <p>
                  <strong>Tên khách hàng:</strong>
                  {{ row.customer ? row.customer.name : '' }}
                </p>
                <p>
                  <strong>Số điện thoại:</strong>
                  {{ row.customer.phone | formatPhone }}
                </p>
                <p>
                  <strong>Email:</strong>
                  {{ row.customer ? row.customer.email : '' }}
                </p>
                <p>
                  <strong>Ngày mua:</strong>
                  {{ row.orderDate | formatDateFromInt }}
                </p>
                <p>
                  <strong>Mã đơn hàng:</strong>
                  {{ row.orderCode }}
                </p>
                <p v-if="campainWithKplus">
                  <strong>Active K+:</strong>
                  {{ row.activeKPlus ? row.activeKPlus : '' }}
                </p>
                <p>
                  <strong>Địa điểm mua:</strong>
                  {{ row.store ? row.store.address : '' }}
                </p>
                <p>
                  <strong>Ngày tạo đơn:</strong>
                  {{ row.created | formatDateFromInt }}
                </p>
                <p class="mb-0">
                  <strong>Trạng thái:</strong>
                  <span v-if="row.process === claimProcess.PENDING.VALUE"
                    class="badge text-wrap badge-info mb-1 lh-120">
                    {{ claimProcess.PENDING.TEXT }}
                  </span>
                  <span v-if="row.process === claimProcess.APPROVED_1.VALUE"
                    class="badge text-wrap badge-success mb-1 lh-120">
                    {{ claimProcess.APPROVED_1.TEXT }}
                  </span>
                  <span v-if="row.process === claimProcess.APPROVED_2.VALUE"
                    class="badge text-wrap badge-success mb-1 lh-120">
                    {{ claimProcess.APPROVED_2.TEXT }}
                  </span>
                  <span v-if="row.process === claimProcess.DENIED_1.VALUE"
                    class="badge text-wrap badge-danger mb-1 lh-120">
                    {{ claimProcess.DENIED_1.TEXT }}
                  </span>
                  <span v-if="row.process === claimProcess.DENIED_2.VALUE"
                    class="badge text-wrap badge-danger mb-1 lh-120">
                    {{ claimProcess.DENIED_2.TEXT }}
                  </span>
                </p>
              </div>
              <div class="col-6">
                <div class="row">
                  <div v-for="item in row.claimDetails" class="col-6 border-bottom">
                    <p>
                      <strong>ID quà:</strong>
                      {{ item.giftId }}
                    </p>
                    <p>
                      <strong>Model:</strong>
                      {{ item.model }}
                    </p>
                    <p>
                      <strong>Số Serial:</strong>
                      {{ item.serial }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="ID" width="100px">
        <template v-slot="{ row }">
          {{ row.id }}
          <span v-if="row.totalField > 0" class="badge text-wrap mb-1 lh-120"
            :class="{ 'badge-success': row.matched, 'badge-danger': !row.matched }">
            {{ row.matchedField ?? 0 }}&nbsp;/&nbsp;{{ row.totalField ?? 0 }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="$permissions.hasAnyPermission(['claims.read', 'claims.create', 'claims.update', 'claims.approve'])"
        label="Thao tác" width="90px" class-name="px-1">
        <template v-slot="{ row }">
          <!--          <div class="w-100 d-flex" style="padding: 0 12px">-->
          <el-tooltip content="Xem chi tiết" placement="top" class="mr-1">
            <NuxtLink :to="'/claims/' + row.id">
              <el-button circle size="small" type="primary" icon="el-icon-edit"></el-button>
            </NuxtLink>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="Tên khách hàng" min-width="180px">
        <template v-slot="{ row }">
          {{ row.customer ? row.customer.name : '' }}
        </template>
      </el-table-column>
      <el-table-column label="Số điện thoại" min-width="160px">
        <template v-slot="{ row }">
          {{ row.customer.phone | formatPhone }}
        </template>
      </el-table-column>
      <el-table-column label="Email" min-width="180px">
        <template v-slot="{ row }">
          {{ row.customer ? row.customer.email : '' }}
        </template>
      </el-table-column>
      <el-table-column label="Ngày mua" min-width="180px">
        <template v-slot="{ row }">
          {{ row.orderDate | formatDateFromInt }}
        </template>
      </el-table-column>
      <el-table-column label="Active K+" min-width="180px" v-if="campainWithKplus">
        <template v-slot="{ row }">
          {{ row?.activeKPlus === 2 ? 'Có' : 'Không' }}
        </template>
      </el-table-column>
      <el-table-column label="Trạng thái" min-width="160px">
        <template v-slot="{ row }">
          <span v-if="row.resendGift" class="badge text-wrap badge-danger mb-1 lh-120">Lỗi gửi quà</span>
          <br v-if="row.resendGift" />
          <span v-if="row.process === claimProcess.PENDING.VALUE" class="badge text-wrap badge-info mb-1 lh-120">
            {{ claimProcess.PENDING.TEXT }}
          </span>
          <span v-if="row.process === claimProcess.IN_PROCESS.VALUE" class="badge text-wrap badge-info mb-1 lh-120">
            {{ claimProcess.IN_PROCESS.TEXT }}
          </span>
          <span v-if="row.process === claimProcess.APPROVED_1.VALUE" class="badge text-wrap badge-success mb-1 lh-120">
            {{ claimProcess.APPROVED_1.TEXT }}
          </span>
          <span v-if="row.process === claimProcess.APPROVED_2.VALUE" class="badge text-wrap badge-success mb-1 lh-120">
            {{ claimProcess.APPROVED_2.TEXT }}
          </span>
          <span v-if="row.process === claimProcess.DENIED_1.VALUE" class="badge text-wrap badge-danger mb-1 lh-120">
            {{ claimProcess.DENIED_1.TEXT }}
          </span>
          <span v-if="row.process === claimProcess.DENIED_2.VALUE" class="badge text-wrap badge-danger mb-1 lh-120">
            {{ claimProcess.DENIED_2.TEXT }}
          </span>

        </template>
      </el-table-column>
      <el-table-column label="Ngày tạo đơn" min-width="180px">
        <template v-slot="{ row }">
          {{ row.created | formatDatetimeFromInt }}
        </template>
      </el-table-column>
      <el-table-column v-if="$permissions.hasPermission(['claims.clone'])" label="Nhân Bản" min-width="80px"
        class-name="px-1">
        <template v-slot="{ row }">
          <el-tooltip v-if="row.process === 5" content="Nhân bản Claim" placement="top" class="ml-1">
            <div class="d-inline-block">
              <el-button circle size="small" type="success" @click="handleCloneClaim(row.id)"
                icon="el-icon-document-copy"></el-button>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!--CARD:FOOTER-->
    <div class="card-footer py-4 d-flex justify-content-end">
      <base-pagination v-if="claimState.items" v-model="params.page" :total="claimState.total"
        :per-page="params.limit"></base-pagination>
    </div>
  </div>
</template>

<script>
import { computed, defineComponent, useContext, useFetch, watch } from '@nuxtjs/composition-api';
import { Table, TableColumn, Button } from 'element-ui';
import { useClaims } from '@/composition';
import { CLAIM_PROCESS } from '@/util/constant.js';
import ExportClaim from '@/components/pages/claim/ExportClaim.vue';
import ConfigCampaign from '../../../configs/campaign.json';
import ImportClaimApprove from "@/components/pages/claim/ImportClaimApprove.vue";

export default defineComponent({
  name: 'ClaimList',
  components: {
    ImportClaimApprove,
    ExportClaim,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Button.name]: Button,
  },
  props: {
    params: Object,
  },
  setup(props) {
    // const store = useStore();
    const { store } = useContext();
    const { fetchClaims, resentGift, cloneClaim } = useClaims();
    const { id: campaignId } = store.getters['campaignStore/currentCampaign'];
    let campainWithKplus;
    try {
      campainWithKplus = ConfigCampaign[campaignId]['active_kplus'] === "true";
    } catch {
      campainWithKplus = ConfigCampaign['0']['active_kplus'] === "true";
    }

    async function fetchData() {
      await store.dispatch('setLoading', true);
      await fetchClaims(props.params);
      await store.dispatch('setLoading', false);
    }

    watch(
      () => props.params.page,
      async () => {
        await fetchData();
      },
    );

    const claimState = computed(() => {
      return store.getters['claim/lists'];
    });

    useFetch(async () => {
      await fetchData();
    });

    const handleResentGift = async (claim) => {
      await store.dispatch('setLoading', true);
      await resentGift(claim.id);
      await fetchClaims(props.params);
      await store.dispatch('setLoading', false);
    };
    const handleCloneClaim = async (claimId) => {
      await store.dispatch('setLoading', true);
      await cloneClaim(claimId).then(async (res) => {
        await store.dispatch('setLoading', false);
        window.location.reload();
      });

    };

    return {
      claimState,
      fetchClaims,
      claimProcess: CLAIM_PROCESS,
      handleResentGift,
      campaignId,
      campainWithKplus,
      handleCloneClaim,

    };
  },
});
</script>

<style lang="scss">
.el-table .cell {
  justify-content: center;
}

.el-table__cell:first-child {
  padding: unset !important;

  .cell {
    padding-left: unset !important;
  }
}
</style>

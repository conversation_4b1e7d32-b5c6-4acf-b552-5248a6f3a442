<template>
  <div>
    <el-button @click="dialogVisible = !dialogVisible" type="primary" icon="el-icon-circle-plus" size="mini">
      Import danh sách duyệt lần {{approveTime}}
    </el-button>
    <ValidationObserver v-slot="{ invalid }">
      <el-dialog
          class="dialog-export"
          title="Import danh sách duyệt"
          :visible.sync="dialogVisible"
          :before-close="handleBeforeCloseDialog"
      >
        <div class="">
          <label for="fname">Import danh sách duyệt lần {{approveTime}} bằng file Excel (tối đa 100 dòng)</label>
          <input
              name="fname"
              id="fname"
              type="file"
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              @change="uploadFile"
              placeholder="File excel approve"
          ><br>
          <a href="https://file-cdn.urbox.vn/import_approve1685608223152253926.xlsx" download>Tải file mẫu!</a><br><br>
          <el-button type="success" icon="el-icon-circle-plus" @click="submitFile">
            Upload!
          </el-button>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="dialogVisible = false">Đóng</el-button>
        </span>
      </el-dialog>
    </ValidationObserver>
  </div>
</template>

<script>
import {computed, ref, useContext} from "@nuxtjs/composition-api";
import {Message} from "element-ui";
import {useClaims} from "~/composition";
import {integer} from "vee-validate/dist/rules";

export default {
  name: "ImportClaimApprove",
  props: {
    params: Object,
    approveTime: integer,
    title: String,
  },
  components: {},
  setup(props) {
    const {store} = useContext();
    const isLoading = ref(false);
    const dialogVisible = ref(false);
    const currentCampaign = store.getters['campaignStore/currentCampaign'];
    const user = computed(() => {
      return store.getters['loggedInUser'];
    });
    const {importExcelApprove} = useClaims();
    // user.value.email/id;
    var approveFile = null;
    const handleBeforeCloseDialog = () => {}
    const uploadFile = async (event) => {
      console.log(`${currentCampaign.id} - ${props.approveTime} - ${user.value.id}`)
      console.log(event.target.files)
      approveFile = event.target.files[0];
      console.log(approveFile)
    };
    const submitFile = async (e) => {
      e.preventDefault()
      await store.dispatch('setLoading', true);
      console.log(`${currentCampaign.id} - ${props.approveTime} - ${user.value.id}`)
      console.log(approveFile)
      if (approveFile != null) {
        console.log("Serial file not null")
        let data = await importExcelApprove(
            approveFile,
            currentCampaign.id,
            props.approveTime,
            user.value.id,
        );
        console.log(data)
        if (data?.code === 200) {
          Message({
            message: data.msg,
            type: 'success',
            duration: 5000,
          });
        }
      } else {
        Message({
          message: "File không được để trống",
          type: 'error',
          duration: 5000,
        });
      }
      await store.dispatch('setLoading', false);
    };
    return {
      isLoading,
      uploadFile,
      submitFile,
      dialogVisible,
      handleBeforeCloseDialog
    }
  }

}
</script>

<style scoped>

</style>

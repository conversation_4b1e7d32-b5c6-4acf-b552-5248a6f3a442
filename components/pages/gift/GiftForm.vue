<template>
  <div>
    <ValidationObserver ref="giftForm" tag="form" v-slot="{ invalid }" @submit.prevent="handleSubmit">
      <div class="row">
        <div class="form-group col-lg-3">
          <ValidationProvider class="form-group" rules="required" v-slot="{ errors }">
            <label for="voucher-code">Model sản phẩm<span class="text-danger pl-1">*</span></label>
            <input :class="{'is-invalid': errors.length > 0}" type="text" class="form-control mb-0"
                   id="voucher-code" placeholder="Model sản phẩm" required
                   v-model="entity.model">
          </ValidationProvider>

        </div>

        <div class="form-group col-lg-3">

          <ValidationProvider class="form-group" rules="required" v-slot="{ errors }">
            <label for="voucher-code">Tên quà<span class="text-danger pl-1">*</span></label>
            <input :class="{'is-invalid': errors.length > 0}" type="text" class="form-control mb-0"
                   id="voucher-code" placeholder="Tên quà" required
                   v-model="entity.title">
          </ValidationProvider>

        </div>
        <div class="form-group col-lg-3">


          <ValidationProvider class="form-group" rules="required|numeric" v-slot="{ errors }">
            <label>Trạng thái</label>
            <el-select
                class="w-100"
                :class="{'is-invalid': errors.length > 0}"
                clearable
                v-model="entity.status"
                placeholder="Trạng thái"
            >
              <el-option
                  v-for="option in URBOX_STATUS"
                  :key="option.VALUE"
                  :label="option.TEXT"
                  :value="option.VALUE"
                  default="2"
              ></el-option>
            </el-select>
          </ValidationProvider>


        </div>
        <div class="form-group col-lg-3">
          <ValidationProvider class="form-group" rules="required|numeric" v-slot="{ errors }">
            <label>Campaign</label>
            <el-select
                class="w-100"
                :class="{'is-invalid': errors.length > 0}"
                clearable
                v-model="entity.campaign_id"
                placeholder="Campaign Id"
            >
              <el-option
                  v-for="option in campaigns"
                  :key="option.id"
                  :label="option.title"
                  :value="option.id"
                  default="2"
              ></el-option>
            </el-select>
          </ValidationProvider>

        </div>
        <div class="form-group col-lg-3">
          <ValidationProvider class="form-group" rules="required|numeric" v-slot="{ errors }">
            <label for="voucher-code">ID quà con<span class="text-danger pl-1">*</span></label>
            <input :class="{'is-invalid': errors.length > 0}" type="number" class="form-control mb-0"
                   id="voucher-code" placeholder="Tên quà" required
                   v-model="entity.gift_detail_id">
          </ValidationProvider>
        </div>
        <div class="form-group col-lg-3">
          <ValidationProvider class="form-group" rules="required|numeric" v-slot="{ errors }">
            <label for="voucher-code">Giá trị quà<span class="text-danger pl-1">*</span></label>
            <input :class="{'is-invalid': errors.length > 0}" type="number" class="form-control mb-0"
                   id="voucher-code" placeholder="Giá trị quà" required
                   v-model="entity.valuex">
          </ValidationProvider>
        </div>
        <div class="form-group col-lg-3">
          <ValidationProvider class="form-group" rules="" v-slot="{ errors }">
            <label for="voucher-code">Model phụ thuộc</label>
            <input :class="{'is-invalid': errors.length > 0}" type="text" class="form-control mb-0"
                   id="voucher-code" placeholder="Model phụ thuộc"
                   v-model="entity.related_model">
          </ValidationProvider>
        </div>


        <!--                        <div class="form-group col-lg-3">-->
        <!--                          <ValidationProvider class="form-group" rules="number" v-slot="{ errors }">-->
        <!--                            <label for="voucher-code">ValueX<span class="text-danger pl-1">*</span></label>-->
        <!--                            <input :class="{'is-invalid': errors.length > 0}" type="number" class="form-control mb-0"-->
        <!--                                   id="voucher-code" placeholder="ValueX"-->
        <!--                                   v-model="entity.valuex">-->
        <!--                          </ValidationProvider>-->
        <!--                        </div>-->
        <div class="form-group col-lg-3">
          <ValidationProvider class="form-group" rules="numeric" v-slot="{ errors }">
            <label>Loại quà</label>
            <el-select
                class="w-100"
                :class="{'is-invalid': errors.length > 0}"
                clearable
                v-model="entity.category_id"
                placeholder="Loại quà"
            >
              <el-option
                  v-for="option in category"
                  :key="option.id"
                  :label="option.title"
                  :value="option.id"
              ></el-option>
            </el-select>
          </ValidationProvider>
        </div>

      </div>
      <div class="row">


        <div class="form-group col-lg-4">
          <div>
            <label class="form-label"></label>
          </div>
          <el-button
              v-if="$permissions.hasAnyPermission(['gifts.create','gifts.update'])"
              type="primary"
              native-type="submit"
          >
            Lưu
          </el-button>
        </div>

      </div>
    </ValidationObserver>
  </div>
</template>

<script>
import {computed, defineComponent, useContext, ref, useRouter, useFetch} from "@nuxtjs/composition-api";
import {Alert, Button, Form, FormItem, Option, Select, Table, TableColumn} from "element-ui";
import {URBOX_STATUS} from '~/util/constant';
import {useGifts, useCategory} from "~/composition";

export default defineComponent({
  name: 'GiftForm',
  layout: 'DashboardLayout',
  props: ['entity','errorMessage','successMessage'],
  components: {
    [Form.name]: Form,
    [FormItem.name]: FormItem,
    [Option.name]: Option,
    [Select.name]: Select,
    [Button.name]: Button,
    [Alert.name]: Alert,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
  },
  setup(props) {
    const {store, params} = useContext();
    const router = useRouter();
    const {entity} = props;
    const giftForm = ref(null)
    const {saveGift} = useGifts();
    const { category, fetchCategory} = useCategory();
    const campaigns = computed(() => {
      return store.getters['campaignStore/campaigns'];
    })
    useFetch(() => fetchCategory());

    async function handleSubmit() {
      try {
        await store.dispatch('setLoading', true);
        giftForm.value.validate().then(async (res) => {
          if (res) {
            await saveGift(entity, params?.value?.id || 0).then(res => {
              router.push('/gifts');
            })
          }
        })
      } catch (e) {
        console.log(e.message)
        if (e.data && e.data.message) {
          props.errorMessage.value = e.data.message;
        }
      } finally {
        await store.dispatch('setLoading', false);
      }
    }


    return {
      handleSubmit,
      entity,
      URBOX_STATUS,
      campaigns,
      giftForm,
      category
    }
  }
})
</script>

<style scoped>

</style>

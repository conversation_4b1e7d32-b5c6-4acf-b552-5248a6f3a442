<template>
  <div class="card" v-if="listGifts">
    <div class="border-0 card-header d-flex justify-content-between">
      <h3 class="mb-0"><PERSON>h sách quà</h3>
      <ImportGift
          v-if="$permissions.hasAnyPermission(['gifts.create'])"
          :approve-time="1"/>
      <!-- <ExportGift :params="params" /> -->
      <p>tổng số quà :{{ listGifts.length }}</p>
    </div>

    <el-table
        class="table-responsive"
        border
        size="mini"
        :highlight-current-row="true"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ justifyContent: 'center', fontWeight: 'bold' }"
        :data="listGifts"
        style="width: 100%"
    >
      <el-table-column label="Thao tác" width="140px" v-if="$permissions.hasAnyPermission(['gifts.create','gifts.update'])">
        <template v-slot="{ row }">
          <!--          <div class="w-100 d-flex" style="padding: 0 12px">-->
          <el-tooltip content="Sửa quà" placement="top" class="mr-2">
            <NuxtLink :to="'/gifts/' + row.id">
              <el-button circle size="small" type="primary" icon="el-icon-edit"></el-button>
            </NuxtLink>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="ID" width="100px">
        <template v-slot="{ row }">
          {{ row.id }}
        </template>
      </el-table-column>
      <el-table-column label="title" min-width="180px">
        <template v-slot="{ row }">
          {{ row.title ? row.title : '' }}
        </template>
      </el-table-column>
      <el-table-column label="Gift detail id" min-width="80px">
        <template v-slot="{ row }">
          {{ row.gift_detail_id ? row.gift_detail_id : '' }}
        </template>
      </el-table-column>
      <el-table-column label="model" min-width="80px">
        <template v-slot="{ row }">
          {{ row.model ? row.model : '' }}
        </template>
      </el-table-column>
      <el-table-column label="Loại quà" min-width="80px">
        <template v-slot="{ row }">
          {{ row.category?.id ? row.category?.title : '' }}
        </template>
      </el-table-column>

      <el-table-column label="Mệnh giá" min-width="80px">
        <template v-slot="{ row }">
          {{ row.valuex ? row.valuex : 'N/A' }}
        </template>
      </el-table-column>
      <el-table-column label="Related model" min-width="80px">
        <template v-slot="{ row }">
          {{ row.related_model ? row.related_model : '' }}
        </template>
      </el-table-column>

      <el-table-column label="Trạng thái" min-width="80px" v-if="$permissions.hasAnyPermission(['gifts.create','gifts.update'])">
        <template v-slot="{ row }">
          <el-switch
              style="display: block; height: 21px"
              v-model="row.status"
              :active-value="2"
              :inactive-value="1"
              active-text=""
              inactive-text=""
              @change="toggleStatus(row,row.status)"
          >
          </el-switch>

        </template>
      </el-table-column>

    </el-table>

    <!--CARD:FOOTER-->
    <div class="card-footer py-4 d-flex justify-content-end">
      <!--      <base-pagination-->
      <!--        v-if="claimState.items"-->
      <!--        v-model="params.page"-->
      <!--        :total="claimState.total"-->
      <!--        :per-page="params.limit"-->
      <!--      ></base-pagination>-->
    </div>
  </div>
</template>

<script>
import {computed, defineComponent, useContext, useFetch, watch} from '@nuxtjs/composition-api';
import {Table, TableColumn, Button, Switch, Message} from 'element-ui';
import {useGifts} from '~/composition';
import {CLAIM_PROCESS, URBOX_STATUS} from '~/util/constant';
import ImportGift from "~/components/pages/gift/ImportGift";
import {useCategory} from "../../../composition";


export default defineComponent({
  name: 'GiftList',
  components: {
    ImportGift,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Button.name]: Button,
    [Switch.name]: Switch,
  },
  props: {
    params: Object,
  },
  setup(props) {
    const {store} = useContext();
    const {fetchGiftList, listGifts, changeStatus, isSuccess} = useGifts();
    const {id: campaignId} = store.getters['campaignStore/currentCampaign'];
    async function fetchData() {
      await store.dispatch('setLoading', true);
      await fetchGiftList(props.params);
      await store.dispatch('setLoading', false);
    }
    const toggleStatus = async (row,status) => {
      await changeStatus(row,status).then(() => {
        if(!isSuccess.value) {
          Message({
            message: "Lỗi thay đổi trạng thái",
            type: 'error',
            duration: 5 * 1000,
          });
        }
      })
    }

    watch(
        () => props.params.page,
        async () => {
          await fetchData();
        },
    );
    useFetch(async () => {
      await fetchData();
    });

    return {
      listGifts,
      claimProcess: CLAIM_PROCESS,
      campaignId,
      toggleStatus,
      URBOX_STATUS
    };
  },
});
</script>

<style lang="scss">
.el-table .cell {
  justify-content: center;
}

.el-table__cell:first-child {
  padding: unset !important;

  .cell {
    padding-left: unset !important;
  }
}
</style>

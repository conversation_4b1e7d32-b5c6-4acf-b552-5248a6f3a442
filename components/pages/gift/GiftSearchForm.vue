<template>
  <div class="row pt-4">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title mb-0">T<PERSON><PERSON> kiếm</h3>
        </div>

        <div class="card-body">
          <form>
            <div class="row">
              <div class="col-4 mb-4">
                <el-input v-model="params.name" placeholder="Tên quà"></el-input>
              </div>
              <div class="col-4 col-md-2 mb-4">
                <el-select
                  class="w-100"
                  @change="handleProcessChange"
                  clearable
                  v-model="params.status"
                  placeholder="Trạng thái"
                >
                  <el-option
                    v-for="option in URBOX_STATUS"
                    :key="option.VALUE"
                    :label="option.TEXT"
                    :value="option.VALUE"
                  ></el-option>
                </el-select>
              </div>

              <div class="col-4">
                <el-button class="" type="primary" icon="el-icon-search" @click="onSearch">
                  T<PERSON><PERSON> kiếm
                </el-button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {computed, defineComponent, ref, useAsync, useContext, watch} from '@nuxtjs/composition-api';
  import { CLAIM_PROCESS_LIST, URBOX_STATUS } from '~/util/constant';
  import { Select, Option, Button } from 'element-ui';
  import useGifts from '~/composition/useGifts';
  import useStores from '~/composition/useStores';

  export default defineComponent({
    name: 'ClaimSearchForm',
    props: {
      params: Object,
    },
    components: {
      [Select.name]: Select,
      [Option.name]: Option,
      [Button.name]: Button,
    },
    setup(props) {
      // const store = useStore();
      const { store } = useContext();
      const isLoading = ref(false);
      const { fetchGiftList } = useGifts();
      const { fetchStores } = useStores();

      const dateRange = ref(null);

      useAsync(async () => {
        await fetchStores({});
      });

      const stores = computed(() => {
        return store.getters['store/items'];
      });

      const onSearch = async () => {
        await store.dispatch('setLoading', true);
        props.params.page = 1;
        props.params.name = props.params.name.trim();
        await fetchGiftList(props.params);
        await store.dispatch('setLoading', false);
      };

      const handleStoreAddressChange = async (search) => {
        isLoading.value = true;
        await fetchStores({ search });
        isLoading.value = false;
      };

      const onOrderStoreChange = (data) => {
        if (!data) {
          props.params.orderStoreId = null;
          handleStoreAddressChange('');
        }
      };

      const handleProcessChange = (input) => {
        if (!input) {
          props.params.process = null;
        }
      };

      const claimProcessType = CLAIM_PROCESS_LIST;

      return {
        claimProcessType,
        onSearch,
        dateRange,
        stores,
        handleStoreAddressChange,
        isLoading,
        onOrderStoreChange,
        handleProcessChange,
        URBOX_STATUS
      };
    },
  });
</script>

<style lang="scss">
  .el-select .el-input .el-input__inner {
    height: 40px;
  }

  .el-date-editor {
    .el-range-separator {
      width: 10% !important;
    }
    .el-icon-date {
      margin-left: 0;
    }
    i {
      width: 8% !important;
    }
  }
</style>

<template>
  <div :class="layoutClass" class="auth-layout">

    <div class="main-content">
      <nuxt></nuxt>
    </div>

    <footer class="py-5" id="footer-main">
      <div class="container">
        <div class="row align-items-center justify-content-center">
          <div class="col-xl-12">
            <div class="copyright text-center text-muted">
              © {{ year }}, A product from
              <a href="https://urbox.vn" class="font-weight-bold ml-1" target="_blank">UrBox</a>
              , made with <i class="fa fa-heart heart"></i>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>
<script>
import BaseNav from "~/components/argon-core/Navbar/BaseNav.vue";

export default {
  components: {
    BaseNav,
  },
  props: {
    backgroundColor: {
      type: String,
      default: "black",
    },
  },
  data() {
    return {
      showMenu: false,
      menuTransitionDuration: 250,
      year: new Date().getFullYear(),
      pageClass: "login-page",
    };
  },
  computed: {
    title() {
      return `${this.$route.name} Page`;
    },
    layoutClass() {
      let exceptions = ["index", "home"];
      if (!exceptions.includes(this.$route.name)) {
        return "bg-default";
      } else {
        return "";
      }
    },
  },
  methods: {
    closeMenu() {
      document.body.classList.remove("nav-open");
      this.showMenu = false;
    },
  },
  watch: {
    "$route.path"() {
      if (this.showMenu) {
        this.closeMenu();
      }
    },
  },
};
</script>
<style lang="scss">
.auth-layout {
  min-height: 100vh;
}
</style>

module.exports = {
  publicRuntimeConfig: {
    baseURL: process.env.BASE_URL,
    baseImageUrl: process.env.BASE_IMAGE_URL,
    imageUrlUpload: process.env.IMAGE_URL_UPLOAD,
    campaignId: process.env.CAMPAIGN_ID || '3',
  },

  privateRuntimeConfig: {},

  meta: {
    ogType: false,
    ogDescription: false,
    author: false,
    ogTitle: false,
    description: false,
    viewport: false,
    charset: false,
  },
  /*
   ** Headers of the page
   */
  head: {
    title: 'Ur Claim Portal',
    meta: [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      {
        hid: 'description',
        name: 'description',
        content: 'Voucher manage',
      },
    ],
    link: [
      { rel: 'icon', type: 'image/png', href: '/favicon.ico' },
      {
        rel: 'stylesheet',
        href: 'https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700',
      },
      {
        rel: 'stylesheet',
        href: 'https://use.fontawesome.com/releases/v5.6.3/css/all.css',
        integrity: 'sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/',
        crossorigin: 'anonymous',
      },
    ],
  },

  /*
   ** Customize the progress-bar color
   */
  loading: { color: '#fff' },

  /*
   ** Global CSS
   */
  css: ['assets/css/nucleo/css/nucleo.css', 'assets/sass/argon.scss', '~assets/css/style.css'],

  auth: {
    strategies: {
      local: {
        token: {
          property: 'token',
          global: true,
          required: true,
          type: 'Bearer',
        },
        endpoints: {
          login: { url: '/user/login', method: 'post' },
          changePassword: { url: '/user/change-password', method: 'put' },
          logout: false,
          user: false,
        },
      },
    },
  },
  router: {
    base: '/',
    linkExactActiveClass: 'active',
    middleware: ['auth', 'permission'],
  },
  plugins: [
    '@/plugins/dashboard/dashboard-plugin',
    '@/plugins/axios', // Cấu hình axios trước
    '@/plugins/loadApi',
    '@/plugins/helper',
    '@/plugins/filter.js',
    '@/plugins/elementUi.js',
    '@/plugins/permissions.js', // Permissions plugin cần user data
  ],
  ssr: false,
  modules: ['@nuxtjs/axios', '@nuxtjs/pwa', '@nuxtjs/auth', '@nuxtjs/toast', 'vue2-editor/nuxt'],
  buildModules: ['@nuxtjs/composition-api/module'],
  toast: {
    position: 'top-right',
    duration: 5000,
    keepOnHover: true,
    fullWidth: false,
    fitToScreen: true,
    className: 'vue-toast-custom',
    closeOnSwipe: true,
    register: [
      {
        name: 'my-error',
        message: 'Oops...Something went wrong',
        options: {
          type: 'error',
        },
      },
    ],
  },
  build: {
    transpile: ['vee-validate/dist/rules'],
    extend(config, ctx) {
    },
    extractCSS: process.env.NODE_ENV === 'production',
    babel: {
      plugins: [
        [
          'component',
          {
            libraryName: 'element-ui',
            styleLibraryName: 'theme-chalk',
          },
        ],
      ],
    },
  },
};

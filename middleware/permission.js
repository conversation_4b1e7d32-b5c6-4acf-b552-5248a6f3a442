const routePermissionMap = {
    '/claims': 'claims.read',
    '/claims/create': 'claims.create',
    '/claims-temp': 'claims.read',
    '/gifts': 'gifts.read',
    '/campaign': 'campaigns.read',
    '/whitelist': 'whitelist.read',
    '/images': 'images.read',
    '/user-management': 'users.read',
    // Thêm các route và quyền tương ứng ở đây
}

// Kiểm tra dynamic routes (như /claims/123)
function getPermissionForDynamicRoute(path) {
    // Kiểm tra claims detail route
    if (path.match(/^\/claims\/\d+$/)) {
        return 'claims.read';
    }

    // Kiểm tra claims-temp detail route  
    if (path.match(/^\/claims-temp\/\d+$/)) {
        return 'claims.read';
    }

    // Kiểm tra whitelist detail route
    if (path.match(/^\/whitelist\/\d+$/)) {
        return 'whitelist.read';
    }

    // Kiểm tra whitelist detail route
    if (path.match(/^\/campaigns\/\d+$/)) {
        return 'campaigns.read';
    }

    return null;
}

export default function ({ app, route, redirect, store }) {
    // Bỏ qua check cho trang login và root
    if (['/login', '/'].includes(route.path)) return

    // Kiểm tra xem user đã đăng nhập chưa
    if (!store.getters.isAuthenticated) {
        console.log('🔄 User not authenticated, redirecting to login');
        return redirect('/login')
    }

    let permission = routePermissionMap[route.path]

    // Nếu không tìm thấy trong static routes, kiểm tra dynamic routes
    if (!permission) {
        permission = getPermissionForDynamicRoute(route.path)
    }

    // Nếu route không có yêu cầu permission cụ thể, cho phép truy cập
    if (!permission) {
        console.log('⏭️ No permission required for route:', route.path);
        return
    }

    // Kiểm tra xem $permissions plugin đã sẵn sàng chưa
    if (typeof app.$permissions?.hasPermission !== 'function') {
        console.warn('⚠️ Permissions plugin not ready, allowing access');
        return // Cho phép truy cập thay vì redirect
    }

    // Nếu user data đã load, kiểm tra permission ngay
    if (app.$permissions.isUserLoaded()) {
        if (!app.$permissions.hasPermission(permission)) {
            console.warn('❌ User does not have permission:', permission);
            return redirect('/')
        }
        console.log('✅ User has permission:', permission);
        return
    }

    // Nếu user data đang loading, chờ một chút (vì có thể đang load từ login hoặc layout)
    if (store.getters.isUserLoading) {
        console.log('⏳ User data is loading, waiting...');
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = 50; // Tối đa 5 giây (50 * 100ms)

            const checkPermission = () => {
                attempts++;

                if (app.$permissions.isUserLoaded()) {
                    // User data đã load, kiểm tra permission
                    if (!app.$permissions.hasPermission(permission)) {
                        console.warn('❌ User does not have permission after loading:', permission);
                        resolve(redirect('/'))
                    } else {
                        console.log('✅ User has permission after loading:', permission);
                        resolve()
                    }
                    return
                }

                if (attempts >= maxAttempts) {
                    // Timeout, cho phép truy cập và để layout xử lý
                    console.warn('⏰ Timeout waiting for user data, allowing navigation');
                    resolve()
                    return
                }

                // Đợi thêm 100ms và thử lại
                setTimeout(checkPermission, 100)
            }

            checkPermission()
        })
    }

    // Nếu user data chưa load và không đang loading, cho phép truy cập
    console.log('⏭️ User data not loaded yet, allowing navigation. Will check permission in layout.');
    return
} 
# Permission Utility Functions - Usage Examples

## Cấu trúc Permission System

Hệ thống permission dựa trên user data được lưu trong **Vuex store** và `$auth.user`. Mỗi permission có cấu trúc:
```javascript
{
  "id": 1,
  "name": "claims.create",
  "display_name": "Tạo Claim",
  "resource": "claims",
  "action": "create",
  "description": "Tạo claim mới"
}
```

## Migration Guide - Thay thế Store Getters

### Old Store Getters → New $permissions Plugin

```javascript
// ❌ OLD: Store getters
// this.$store.getters.getCanCreateClaim
// this.$store.getters.getCantUpdateOrder
// this.$store.getters.getCanChangeOcr

// ✅ NEW: Plugin functions
this.$permissions.canCreateClaim()
this.$permissions.cantUpdateOrder()
this.$permissions.canChangeOcr()
```

### Template Usage Examples

```vue
<!-- OLD -->
<!-- <button v-if="$store.getters.getCanCreateClaim">Create</button> -->
<!-- <div v-if="!$store.getters.getCantUpdateOrder">Update</div> -->
<!-- <span v-if="$store.getters.getCanChangeOcr">Change OCR</span> -->

<!-- NEW -->
<button v-if="$permissions.canCreateClaim()">Create</button>
<div v-if="$permissions.cantUpdateOrder()">Update</div>
<span v-if="$permissions.canChangeOcr()">Change OCR</span>
```

### Script Usage Examples

```javascript
// OLD - Options API
// if (this.$store.getters.getCanCreateClaim) { /* ... */ }

// NEW - Options API  
if (this.$permissions.canCreateClaim()) { /* ... */ }

// OLD - Composition API
// const canCreate = store.getters['getCanCreateClaim'];

// NEW - Composition API
const { $permissions } = useContext();
const canCreate = $permissions.canCreateClaim();
```

## Flow hoạt động

1. **User đăng nhập** → Token được lưu
2. **Layout load** → Gọi `store.dispatch('loadUserData')`
3. **API `/user/me`** → Trả về user data + permissions
4. **Store + Auth** → User data được lưu trong cả Vuex store và `$auth.user`
5. **Permission system** → Sử dụng store data làm primary, fallback to `$auth.user`
6. **Components** → Sử dụng permissions thông qua `$permissions` plugin

## Data Source Priority

Hệ thống ưu tiên data source theo thứ tự:
1. **Store data** (`store.getters.getUserData`) - Primary
2. **Auth data** (`$auth.user`) - Fallback

```javascript
// Plugin sử dụng logic này
const getUserData = () => {
    const storeUserData = store.getters.getUserData;
    if (storeUserData) {
        return storeUserData;
    }
    return $auth?.user;
};
```

## Cách sử dụng trong Vue components

### 1. Sử dụng trong template

```vue
<template>
  <div>
    <!-- Kiểm tra permission trực tiếp -->
    <button v-if="$permissions.canCreateClaim()">Tạo Claim</button>
    <div v-if="$permissions.canManageGifts()">Quản lý quà tặng</div>
    
    <!-- Kiểm tra permission cụ thể -->
    <div v-if="$permissions.hasPermission('claims.create')">
      Create Claims
    </div>
    
    <!-- Kiểm tra theo resource và action -->
    <div v-if="$permissions.canPerformAction('claims', 'create')">
      Create Claims
    </div>
    
    <!-- Kiểm tra loading state -->
    <div v-if="!$permissions.isUserLoaded()">Loading permissions...</div>
  </div>
</template>
```

### 2. Sử dụng trong Options API

```vue
<script>
export default {
  mounted() {
    // Kiểm tra user đã load chưa
    if (this.$permissions.isUserLoaded()) {
      console.log('User permissions:', this.$permissions.getUserPermissions());
    }
    
    // Kiểm tra permission cụ thể
    if (this.$permissions.hasPermission('claims.create')) {
      console.log('User can create claims');
    }
    
    // Kiểm tra theo resource và action
    if (this.$permissions.canPerformAction('gifts', 'manage')) {
      console.log('User can manage gifts');
    }
  }
}
</script>
```

### 3. Sử dụng trong Composition API

```vue
<script>
import { defineComponent, useContext } from '@nuxtjs/composition-api';

export default defineComponent({
  setup() {
    const { $permissions } = useContext();
    
    // Kiểm tra permissions
    console.log('Can create claim:', $permissions.canCreateClaim());
    console.log('User data:', $permissions.getUserData());
    console.log('Is user loaded:', $permissions.isUserLoaded());
    
    return {};
  }
});
</script>
```

## Plugin Functions ($permissions)

### Core Functions
- `$permissions.hasPermission(permissionName)` - Kiểm tra permission theo tên
- `$permissions.canPerformAction(resource, action)` - Kiểm tra theo resource và action
- `$permissions.isUserLoaded()` - Kiểm tra user đã load chưa
- `$permissions.getUserPermissions()` - Lấy tất cả permissions
- `$permissions.getUserData()` - Lấy user data

### Specific Permission Checks
- `$permissions.canCreateClaim()` - Kiểm tra quyền tạo claim
- `$permissions.canManageGifts()` - Kiểm tra quyền quản lý gifts
- `$permissions.canManageCampaigns()` - Kiểm tra quyền quản lý campaigns
- `$permissions.canViewClaim()` - Kiểm tra quyền xem claim
- `$permissions.canEditClaim()` - Kiểm tra quyền edit claim
- `$permissions.canDeleteClaim()` - Kiểm tra quyền xóa claim

## Store Actions và Getters

### Actions
```javascript
// Load user data (called automatically in layout)
await this.$store.dispatch('loadUserData', { $axios, $auth, $config });

// Clear user data (on logout)
this.$store.dispatch('clearUserData');

// Set user data manually
this.$store.dispatch('setUserData', userData);
```

### Getters
```javascript
// User state
this.$store.getters.isUserLoaded        // boolean
this.$store.getters.getUserData         // user object

// Loading state
this.$store.getters.getIsLoading        // boolean
```

## Hybrid System (Backward Compatible)

Hệ thống hỗ trợ cả:
1. **New permission system** - Dựa trên permissions array từ API
2. **Old permission system** - Dựa trên email lists trong config

```javascript
// Logic tự động chọn system trong util/permissions.js
if (userHasPermissionsArray) {
  return newPermissionSystem.check();
} else {
  return oldPermissionSystem.check();
}
```

## Ví dụ Integration với Auth

```javascript
// Trong auth login success
async onLoginSuccess() {
  // User data sẽ được load tự động bởi layout
  // Hoặc manual load:
  await this.$store.dispatch('loadUserData', {
    $axios: this.$axios,
    $auth: this.$auth,
    $config: this.$config
  });
}

// Trong logout
async onLogout() {
  this.$store.dispatch('clearUserData');
  await this.$auth.logout();
}
```

## Debug và Troubleshooting

```javascript
// Check user load state
console.log('User loaded:', this.$permissions.isUserLoaded());
console.log('User data:', this.$permissions.getUserData());
console.log('Permissions:', this.$permissions.getUserPermissions());

// Manual reload
await this.$store.dispatch('loadUserData', {
  $axios: this.$axios,
  $auth: this.$auth,
  $config: this.$config
});

// Test specific permissions
console.log('Can create claims:', this.$permissions.canCreateClaim());
console.log('Has permission:', this.$permissions.hasPermission('claims.create'));
``` 
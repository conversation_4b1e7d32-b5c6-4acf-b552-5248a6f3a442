# Danh sách API ph<PERSON>uy<PERSON> (RBAC)

## 1. <PERSON><PERSON><PERSON> danh sách tất cả roles
- **GET** `/v1/roles`
- **<PERSON><PERSON> tả:** L<PERSON>y danh sách các vai trò (roles) trong hệ thống.
- **Yêu cầu:** Bearer <PERSON>, permission `roles.read`

---

## 2. Tạo role mới
- **POST** `/v1/roles`
- **Body:**
  ```json
  {
    "name": "custom_role",
    "display_name": "Custom Role",
    "description": "Vai trò tùy chỉnh",
    "permission_ids": [1, 2, 3]
  }
  ```
- **Yêu cầu:** Bearer <PERSON>, permission `roles.create`

---

## 3. <PERSON><PERSON><PERSON> chi tiết một role
- **GET** `/v1/roles/{role_id}`
- **Yêu cầu:** Bearer <PERSON>, permission `roles.read`

---

## 4. <PERSON><PERSON><PERSON> nh<PERSON>t role
- **PUT** `/v1/roles/{role_id}`
- **Body:**
  ```json
  {
    "name": "updated_role",
    "display_name": "Updated Role",
    "description": "<PERSON>ô tả mới",
    "permission_ids": [1, 2, 3, 4]
  }
  ```
- **Yêu cầu:** Bearer Token, permission `roles.update`

---

## 5. Xóa role
- **DELETE** `/v1/roles/{role_id}`
- **Yêu cầu:** Bearer Token, permission `roles.delete`

---

## 6. Lấy danh sách tất cả permissions
- **GET** `/v1/permissions`
- **Yêu cầu:** Bearer Token, permission `permissions.read`

---

## 7. Gán role cho user
- **PUT** `/v1/users/{user_id}/role`
- **Body:**
  ```json
  {
    "role_id": 2
  }
  ```
- **Yêu cầu:** Bearer Token, permission `users.update`

---

## 8. Lấy thông tin user kèm role và permissions
- **GET** `/v1/user/me`
- **Yêu cầu:** Bearer Token

---

## Tóm tắt bảng API RBAC

| Method | Endpoint                  | Chức năng                | Yêu cầu quyền         |
|--------|---------------------------|--------------------------|-----------------------|
| GET    | /v1/roles                 | Danh sách roles          | roles.read            |
| POST   | /v1/roles                 | Tạo role                 | roles.create          |
| GET    | /v1/roles/{role_id}       | Chi tiết role            | roles.read            |
| PUT    | /v1/roles/{role_id}       | Cập nhật role            | roles.update          |
| DELETE | /v1/roles/{role_id}       | Xóa role                 | roles.delete          |
| GET    | /v1/permissions           | Danh sách permissions    | permissions.read      |
| PUT    | /v1/users/{user_id}/role  | Gán role cho user        | users.update          |
| GET    | /v1/user/me               | Thông tin user + quyền   | Đã đăng nhập          |

---

**Lưu ý:**  
- Tất cả các endpoint đều yêu cầu gửi header `Authorization: Bearer {token}`.
- Để lấy danh sách permission_id, hãy gọi `/v1/permissions`.
- Để lấy danh sách role_id, hãy gọi `/v1/roles`. 
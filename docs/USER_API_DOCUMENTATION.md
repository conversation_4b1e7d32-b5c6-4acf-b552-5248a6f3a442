# User Management API Documentation

## Overview
This document provides comprehensive documentation for user management APIs including CRUD operations and role assignments. All endpoints require proper authentication and permissions.

## Authentication
All endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Endpoints

### 1. List Users
**GET** `/v1/users`

Lists all users with pagination support.

**Required Permission:** `users:read`

**Query Parameters:**
| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| campaign_id | integer | true | 0 | Campaign ID filter |
| page | integer | false | 1 | Page number for pagination |
| limit | integer | false | 50 | Number of items per page |

**Example Request:**
```bash
curl -X GET "https://api.example.com/v1/users?campaign_id=1&page=1&limit=20" \
  -H "Authorization: Bearer <jwt_token>"
```

**Example Response:**
```json
{
  "success": true,
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "phone": 1234567890,
      "campaign_id": 1,
      "role_id": 2,
      "status": 1,
      "created": 1640995200,
      "updated": 1640995200,
      "role": {
        "id": 2,
        "name": "user",
        "display_name": "Regular User",
        "description": "Standard user role"
      }
    }
  ],
  "page": 1,
  "pages": 5,
  "per_page": 20,
  "total": 100,
  "has_next": true,
  "has_previous": false,
  "next_page": 2,
  "previous_page": null
}
```

### 2. Get User Details
**GET** `/v1/users/{user_id}`

Retrieves detailed information about a specific user including role and permissions.

**Required Permission:** `users:read`

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| user_id | integer | true | User ID |

**Example Request:**
```bash
curl -X GET "https://api.example.com/v1/users/1" \
  -H "Authorization: Bearer <jwt_token>"
```

**Example Response:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": 1234567890,
    "campaign_id": 1,
    "role_id": 2,
    "status": 1,
    "created": 1640995200,
    "updated": 1640995200,
    "role": {
      "id": 2,
      "name": "user",
      "display_name": "Regular User",
      "description": "Standard user role"
    },
    "permissions": [
      {
        "id": 1,
        "name": "read_claims",
        "display_name": "Read Claims",
        "resource": "claims",
        "action": "read",
        "description": "Permission to view claims"
      }
    ]
  }
}
```

### 3. Create User
**POST** `/v1/users`

Creates a new user in the system.

**Required Permission:** `users:create`

**Request Body:**
```json
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "phone": 9876543210,
  "campaign_id": 1,
  "role_id": 2,
  "pin": "hashed_password",
  "status": 1
}
```

**Field Descriptions:**
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| name | string | false | User's full name |
| email | string | false | User's email address |
| phone | integer | false | User's phone number |
| campaign_id | integer | false | Associated campaign ID |
| role_id | integer | false | Role ID to assign |
| pin | string | false | Hashed password |
| status | integer | false | User status (defaults to 1) |

**Example Request:**
```bash
curl -X POST "https://api.example.com/v1/users" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "phone": 9876543210,
    "campaign_id": 1,
    "role_id": 2
  }'
```

**Example Response:**
```json
{
  "success": true,
  "code": 201,
  "data": {
    "id": 2,
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "phone": 9876543210,
    "campaign_id": 1,
    "role_id": 2,
    "status": 1,
    "created": 1640995200,
    "updated": 1640995200,
    "role": {
      "id": 2,
      "name": "user",
      "display_name": "Regular User",
      "description": "Standard user role"
    },
    "permissions": [
      {
        "id": 1,
        "name": "read_claims",
        "display_name": "Read Claims",
        "resource": "claims",
        "action": "read",
        "description": "Permission to view claims"
      }
    ]
  }
}
```

### 4. Update User
**PUT** `/v1/users/{user_id}`

Updates an existing user's information.

**Required Permission:** `users:update`

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| user_id | integer | true | User ID |

**Request Body:**
```json
{
  "name": "John Updated",
  "email": "<EMAIL>",
  "phone": 1111111111,
  "role_id": 3
}
```

**Example Request:**
```bash
curl -X PUT "https://api.example.com/v1/users/1" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Updated",
    "email": "<EMAIL>"
  }'
```

**Example Response:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "id": 1,
    "name": "John Updated",
    "email": "<EMAIL>",
    "phone": 1234567890,
    "campaign_id": 1,
    "role_id": 2,
    "status": 1,
    "created": 1640995200,
    "updated": 1640995300,
    "role": {
      "id": 2,
      "name": "user",
      "display_name": "Regular User",
      "description": "Standard user role"
    },
    "permissions": [
      {
        "id": 1,
        "name": "read_claims",
        "display_name": "Read Claims",
        "resource": "claims",
        "action": "read",
        "description": "Permission to view claims"
      }
    ]
  }
}
```

### 5. Delete User (Soft Delete)
**DELETE** `/v1/users/{user_id}`

Soft deletes a user (sets status to deleted).

**Required Permission:** `users:delete`

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| user_id | integer | true | User ID |

**Example Request:**
```bash
curl -X DELETE "https://api.example.com/v1/users/1" \
  -H "Authorization: Bearer <jwt_token>"
```

**Example Response:**
```json
{
  "success": true,
  "code": 200,
  "message": "User deleted (soft)"
}
```

### 6. Assign Role to User
**PUT** `/v1/users/{user_id}/role`

Assigns a role to a specific user.

**Required Permission:** `users:update`

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| user_id | integer | true | User ID |

**Request Body:**
```json
{
  "role_id": 3
}
```

**Example Request:**
```bash
curl -X PUT "https://api.example.com/v1/users/1/role" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "role_id": 3
  }'
```

**Example Response:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": 1234567890,
    "campaign_id": 1,
    "role_id": 3,
    "status": 1,
    "created": 1640995200,
    "updated": 1640995400,
    "role": {
      "id": 3,
      "name": "admin",
      "display_name": "Administrator",
      "description": "Full system access"
    }
  }
}
```

### 7. Change User Password
**PUT** `/v1/users/{user_id}/password`

Changes the password of a specific user. This is an admin function that doesn't require current password verification but requires password confirmation.

**Password Requirements:**
- Must be 8-30 characters long
- Must contain at least one letter (a-z or A-Z)
- Must contain at least one number (0-9)

**Required Permission:** `users:update`

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| user_id | integer | true | User ID |

**Request Body:**
```json
{
  "password": "newpass123",
  "confirm_password": "newpass123"
}
```

**Field Descriptions:**
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| password | string | true | New password (8-30 characters, must contain letters and numbers) |
| confirm_password | string | true | Confirmation of new password |

**Example Request:**
```bash
curl -X PUT "https://api.example.com/v1/users/1/password" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "password": "newpass123",
    "confirm_password": "newpass123"
  }'
```

**Example Response:**
```json
{
  "success": true,
  "code": 200,
  "message": "Password changed successfully"
}
```

**Error Responses:**
```json
{
  "success": false,
  "code": 404,
  "message": "User not found"
}
```

```json
{
  "success": false,
  "code": 400,
  "message": "Password and confirm password do not match"
}
```

```json
{
  "success": false,
  "code": 400,
  "message": "Password must be at least 8 characters long"
}
```

```json
{
  "success": false,
  "code": 400,
  "message": "Password must be at most 30 characters long"
}
```

```json
{
  "success": false,
  "code": 400,
  "message": "Password must contain at least one letter"
}
```

```json
{
  "success": false,
  "code": 400,
  "message": "Password must contain at least one number"
}
```

## Error Responses

### Common Error Codes
| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input data |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - User not found |
| 500 | Internal Server Error |

### Error Response Format
```json
{
  "success": false,
  "code": 404,
  "message": "User not found"
}
```

## Security Notes

1. **Authentication**: All endpoints require a valid JWT token
2. **Authorization**: Each endpoint checks specific permissions using RBAC
3. **Data Protection**: Sensitive fields (pin, otp, otp_expired) are automatically excluded from responses
4. **Soft Delete**: Users are soft-deleted to maintain data integrity
5. **Input Validation**: All input data is validated before processing

## RBAC Permissions Required

| Endpoint | Permission Required |
|----------|-------------------|
| GET /users | users:read |
| GET /users/{id} | users:read |
| POST /users | users:create |
| PUT /users/{id} | users:update |
| DELETE /users/{id} | users:delete |
| PUT /users/{id}/role | users:update |
| PUT /users/{id}/password | users:update |

## Usage Examples

### Creating a User with Role Assignment
```bash
# Step 1: Create user
curl -X POST "https://api.example.com/v1/users" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "New User",
    "email": "<EMAIL>",
    "phone": 1234567890
  }'

# Step 2: Assign role (if not set during creation)
curl -X PUT "https://api.example.com/v1/users/2/role" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{"role_id": 2}'
```

### Updating User Information
```bash
curl -X PUT "https://api.example.com/v1/users/1" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Name",
    "phone": 9999999999
  }'
```

### Fetching Users with Pagination
```bash
curl -X GET "https://api.example.com/v1/users?page=2&limit=10&campaign_id=1" \
  -H "Authorization: Bearer <jwt_token>"
```

### Changing User Password
```bash
curl -X PUT "https://api.example.com/v1/users/1/password" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "password": "newpass123",
    "confirm_password": "newpass123"
  }'
``` 
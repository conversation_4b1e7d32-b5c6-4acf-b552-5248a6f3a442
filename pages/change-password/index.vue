<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12">
          <ChangePasswordForm />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { defineComponent, reactive } from '@nuxtjs/composition-api';
  import ChangePasswordForm from './ChangePasswordForm.vue';

  export default defineComponent({
    layout: 'DashboardLayout',
    components: { ChangePasswordForm },
    setup() {
      const params = reactive({
        name: '',
        phone: null,
        email: '',
        model: '',
        serial: '',
        process: null,
        orderDate: null,
        orderStoreId: null,
        page: 1,
        limit: 10,
      });

      return { params };
    },
  });
</script>

<style scoped></style>

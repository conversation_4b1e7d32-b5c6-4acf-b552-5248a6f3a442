<template>
  <div class="row pt-4 d-flex justify-content-center">
    <div class="col-lg-6">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title mb-0"><PERSON><PERSON><PERSON> mật khẩu</h3>
        </div>

        <div class="card-body">
          <form>
            <div class="row">
              <div class="col-md-12 mb-4">
                <span class="demo-input-label ml-1">
                  Mật khẩu hiện tại
                  <span style="color: red">*</span>
                </span>
                <el-input
                  placeholder="Nhập mật khẩu hiện tại"
                  prefix-icon="el-icon-lock"
                  show-password
                  icon="el-icon-search"
                  v-model="password.current"
                  v-on:input="checkPass"
                ></el-input>
                <span class="text-danger ml-1" v-if="errors.current">
                  <i>{{ errors.current }}</i>
                </span>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12 mb-4">
                <span class="demo-input-label ml-1">
                  M<PERSON><PERSON> khẩu mới
                  <span style="color: red">*</span>
                </span>
                <el-input
                  placeholder="Nhập mật khẩu mới"
                  prefix-icon="el-icon-lock"
                  show-password
                  icon="el-icon-search"
                  v-model="password.new"
                  v-on:input="checkPass"
                ></el-input>
                <span class="text-danger ml-1" v-if="errors.new">
                  <i>{{ errors.new }}</i>
                </span>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12 mb-4">
                <span class="demo-input-label ml-1">
                  Xác nhận mật khẩu
                  <span style="color: red">*</span>
                </span>
                <el-input
                  placeholder="Nhập lại mật khẩu mới"
                  prefix-icon="el-icon-lock"
                  show-password
                  icon="el-icon-search"
                  v-on:input="checkPass"
                  v-model="password.confirm"
                ></el-input>
                <span class="text-danger ml-1" v-if="errors.confirm">
                  <i>{{ errors.confirm }}</i>
                </span>
              </div>
            </div>
            <div class="col-md-12 d-flex justify-content-center">
              <el-button
                :disabled="!canSave || !password.confirm || !password.new || !password.current"
                class="col-md-6 col-sm-12"
                type="primary"
                @click="onSearch()"
                icon="el-icon-refresh"
              >
                Đổi mật khẩu
              </el-button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { computed, defineComponent, ref, useAsync, useContext, useStore, watch } from '@nuxtjs/composition-api';
  import { CLAIM_PROCESS_LIST } from '@/util/constant.js';
  import { Select, Option, DatePicker, Button } from 'element-ui';
  import useClaims from '@/composition/useClaims.js';
  import moment from 'moment';

  export default defineComponent({
    name: 'ClaimSearchForm',
    setup() {
      const password = ref({
        current: '',
        new: '',
        confirm: '',
      });
      const canSave = ref(true);
      const errors = ref({
        current: '',
        new: '',
        confirm: '',
      });
      const { store } = useContext();
      const user = computed(() => {
        return store.getters['loggedInUser'];
      });
      const checkPass = () => {
        canSave.value = true;
        errors.value = {
          current: '',
          new: '',
          confirm: '',
        };
        errors.value.current = checkMinLenth(password.value.current);
        errors.value.new = checkMinLenth(password.value.new);
        errors.value.confirm = checkMinLenth(password.value.confirm);
        if (canSave.value && password.value.new) {
          if (password.value.current.localeCompare(password.value.new) === 0) {
            errors.value.new = 'Không được sử dụng mật khẩu cũ';
            canSave.value = false;
            return false;
          }
          const userName = user.value.name.toString().split(' ');
          if (userName && password.value.new) {
            for (let i in userName) {
              if (password.value.new.toLowerCase().includes(userName[i].toLowerCase())) {
                errors.value.new = 'Không sử dụng mật khẩu chứa tên tài khoản hoặc một phần tên của người dùng.';
                canSave.value = false;
                return false;
              }
              if (password.value.new.toLowerCase().includes(user.value.email.toLowerCase())) {
                errors.value.new = 'Không sử dụng mật khẩu chứa có chứa email.';
                canSave.value = false;
                return false;
              }
            }
          }
        }
        if (canSave.value && password.value.confirm) {
          if (password.value.confirm.localeCompare(password.value.new) !== 0) {
            errors.value.confirm = 'Mật khẩu xác nhận không đúng';
            canSave.value = false;
            return false;
          }
        }

        canSave.value = true;
      };

      const checkMinLenth = (text) => {
        let pattern =
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~])[A-Za-z\d!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~]{8,}$/;
        if (text && !pattern.test(text)) {
          canSave.value = false;
          return 'Mật khẩu Ít nhất 8 ký tự, bao gồm số, chữ thường, chữ in hoa và các ký tự đặc biệt';
        }
        return '';
      };
      return { user, checkPass, checkMinLenth, password, errors, canSave };
    },
  });
</script>

<style lang="scss">
  .el-select .el-input .el-input__inner {
    height: 40px;
  }

  .el-date-editor {
    .el-range-separator {
      width: 10% !important;
    }
    .el-icon-date {
      margin-left: 0;
    }
    i {
      width: 8% !important;
    }
  }
</style>

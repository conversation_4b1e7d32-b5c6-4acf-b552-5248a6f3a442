<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12">
          <ImageSearchForm :params="params"></ImageSearchForm>
        </div>
        <div class="col-lg-12">
          <ImageList :params="params"></ImageList>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {defineComponent, reactive} from '@nuxtjs/composition-api';
import ImageList from '@/components/pages/images/ImageList';
import ImageSearchForm from "@/components/pages/images/ImageSearchForm";

export default defineComponent({
  layout: 'DashboardLayout',
  components: { ImageList, ImageSearchForm},
  setup() {
    const params = reactive({
      startDate: null,
      endDate: null,
      page: 1,
      limit: 15,
    });

    return {params};
  },
});
</script>

<style scoped></style>

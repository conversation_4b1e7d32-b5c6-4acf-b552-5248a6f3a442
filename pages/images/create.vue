<template>
  <div>
    <div class="container-fluid mt-4">
      <el-card shadow="always" class="box-card">
        <div slot="header" class="clearfix">
          <h1>Upload ảnh</h1>
        </div>
        <ValidationObserver v-slot="{ invalid }">
          <div class="row justify-content-center">
            <div class="col-lg-8">
              <div class="row">

                <div class="col-lg-12 form-group">
                  <input
                      type="file"
                      accept="image/*"
                      @change="handleImage"
                      multiple
                  />
                </div>
                <div class="col-lg-12 form-group">
                  <el-button
                      :loading="isLoading"
                      type="success"
                      @click="handleUpload"
                      icon="el-icon-upload"
                  >
                    T<PERSON>i lên ảnh
                  </el-button>
                </div>
              </div>

            </div>
          </div>
        </ValidationObserver>
      </el-card>
    </div>
  </div>
</template>

<script>
import {defineComponent, ref, useContext, useRouter} from '@nuxtjs/composition-api';
import {Card, Form, FormItem, Input, Button, DatePicker, Upload, Message} from 'element-ui';
import {getApiErrorMessage} from '@/util/functions.js';
import useImage from '@/composition/useImage.js';
import {clientApi} from '@/util/client-api.js';
import useImages from "@/composition/useImages";

export default defineComponent({
  name: 'ClaimCreate',
  layout: 'DashboardLayout',
  components: {
    [Form.name]: Form,
    [FormItem.name]: FormItem,
    [Input.name]: Input,
    [Card.name]: Card,
    [Button.name]: Button,
    [DatePicker.name]: DatePicker,
    [Upload.name]: Upload,
  },
  setup() {

    const {createImage} = useImages();
    const isLoading = ref(false);

    const images = ref(null);
    const handleImage = (event) => {
      images.value = event.target.files;
    }

    async function handleUpload() {
      try {
        isLoading.value = true;
        console.log(images.value)
        Object.values(images.value).map(async (img) => {
          await createImage(img);
        })

        isLoading.value = false;
      } catch (e) {
        isLoading.value = false;
        console.log(e)
        Message({
          type: 'error',
          message: getApiErrorMessage(e.response?.data?.error?.code || e.message),
          duration: 5000
        });
      }
    }

    return {
      handleUpload,
      isLoading,
      handleImage
    };
  },
});
</script>

<style scoped>
span {
  font-size: 14px;
}
</style>

<template>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12">
<!--          <GiftSearchForm :params="params" />-->
        </div>
        <div class="col-lg-12 mt-5">
           <GiftList :params="params" />
        </div>
      </div>
    </div>
</template>

<script>
  import { defineComponent, reactive } from '@nuxtjs/composition-api';
  import GiftList from '../../components/pages/gift/GiftList.vue';
  import GiftSearchForm from '../../components/pages/gift/GiftSearchForm.vue';

  export default defineComponent({
    layout: 'DashboardLayout',
    components: { GiftList, GiftSearchForm },
    setup() {
      const params = reactive({
        name: '',
        status: null,
        page: 1,
        limit: 10,
        campaign_id: 33
      });

      return { params };
    },
  });
</script>

<style scoped></style>

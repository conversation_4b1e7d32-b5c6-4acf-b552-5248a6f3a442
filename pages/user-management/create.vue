<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12">
          <UserCreate />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api'
import UserCreate from '~/components/user-management/UserCreate.vue'

export default defineComponent({
  layout: 'DashboardLayout',
  components: { UserCreate },
  middleware: ['auth', 'permission'],
  meta: {
    permissions: ['users:create', 'users.create']
  }
})
</script> 
<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12">
          <UserManagement />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api'
import UserManagement from '~/components/user-management/UserManagement.vue'

export default defineComponent({
  layout: 'DashboardLayout',
  components: { UserManagement },
  middleware: ['auth', 'permission'],
  meta: {
    permissions: ['users:read', 'users.read']
  }
})
</script> 
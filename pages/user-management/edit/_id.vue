<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12">
          <UserEdit :userId="$route.params.id" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from '@nuxtjs/composition-api'
import UserEdit from '~/components/user-management/UserEdit.vue'

export default defineComponent({
  layout: 'DashboardLayout',
  components: { UserEdit },
  middleware: ['auth', 'permission'],
  meta: {
    permissions: ['users:update', 'users.update']
  }
})
</script> 
<template>
  <div>
    <div class="container-fluid mt-4">
      <el-card shadow="always" class="box-card">
        <div slot="header" class="clearfix">
          <h1>Tạo mới claim</h1>
        </div>
        <ValidationObserver v-slot="{ invalid }">
          <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="row">
                  <div class="col-lg-12 form-group">
                    <h3>Thêm claim ngoại lệ bằng file Excel (tối đa 100 claims)</h3>
                  </div>
                  <div class="col-lg-12 form-group">
                    <input
                        name="fname"
                        id="fname"
                        type="file"
                        icon="el-icon-search"
                        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                        @change="uploadFile"
                        placeholder="File excel serial"
                    >
                  </div>
                  <div class="col-lg-12 form-group">
                    <a href="/template/template_create_claim.xlsx" download>>Tải file mẫu!</a>
                  </div>
                  <div class="col-lg-12 form-group">
                    <a href="https://file-cdn.urbox.vn/create_claim_guide1678949758284140603.pdf" download>Tải hướng dẫn sử dụng!</a>
                  </div>
                  <div class="col-lg-12 form-group">
                    <el-button type="success" icon="el-icon-circle-plus" @click="submitFile">
                        Upload!
                    </el-button>
                  </div>
                </div>
            </div>
          </div>
          <hr>
          <div class="row justify-content-center">
            <div class="col-lg-8">
              <div class="row">
                <div class="col-lg-12 form-group">
                  <h3>Thêm claim ngoại lệ thủ công</h3>
                  <ValidationProvider
                    name="Họ tên khách hàng"
                    rules="required"
                    v-slot="{ errors }"
                  >
                    <label>
                      Họ tên khách hàng
                      <b class="text-danger">*</b>
                    </label>
                    <el-input
                      v-model="form.name"
                      placeholder="Nhập họ và tên khách hàng"
                    />
                    <span
                      class="text-danger"
                      v-if="errors.length"
                      >{{ errors[0] }}</span
                    >
                  </ValidationProvider>
                </div>

                <div class="form-group col-lg-12">
                  <ValidationProvider
                    name="Số điện thoại"
                    rules="required|min:10|max:11"
                    v-slot="{ errors }"
                  >
                    <label>
                      Số điện thoại
                      <b class="text-danger">*</b>
                    </label>
                    <el-input
                      type="number"
                      :max="11"
                      v-model="form.phone"
                      placeholder="Nhập số điện thoại"
                      maxlength="11"
                    />
                    <span
                      class="text-danger"
                      v-if="errors.length"
                      >{{ errors[0] }}</span
                    >
                  </ValidationProvider>
                </div>

                <div class="form-group col-lg-12">
                  <ValidationProvider
                    name="CMND/CCCD"
                    rules="max:12"
                    v-slot="{ errors }"
                  >
                    <label>CMND/CCCD</label>
                    <el-input
                      type="number"
                      :max="11"
                      v-model="form.identification"
                      placeholder="CMND/CCCD"
                      maxlength="12"
                    />
                    <span
                      class="text-danger"
                      v-if="errors.length"
                      >{{ errors[0] }}</span
                    >
                  </ValidationProvider>
                </div>

                <div class="form-group col-lg-12">
                  <ValidationProvider
                    name="Email"
                    rules="email"
                    v-slot="{ errors }"
                  >
                    <label>Email</label>
                    <el-input v-model="form.email" placeholder="Nhập email" />
                    <span
                      class="text-danger"
                      v-if="errors.length"
                      >{{ errors[0] }}</span
                    >
                  </ValidationProvider>
                </div>

                <div class="form-group col-lg-12">
                  <label>
                    Ngày mua
                    <b class="text-danger">*</b>
                  </label>
                  <el-date-picker
                    v-model="form.orderDate"
                    format="dd/MM/yyyy"
                    placeholder="Chọn ngày mua hàng"
                    class="w-100"
                  />
                </div>

                <div class="form-group col-lg-12">
                  <label>
                    Ảnh hoá đơn
                    <b class="text-danger">*</b>
                  </label>
                  <el-upload
                    action="#"
                    accept="image/*"
                    :auto-upload="false"
                    :multiple="false"
                    :on-change="handleOrderImageChange"
                    list-type="picture"
                    :file-list="orderImageList"
                    :show-file-list="true"
                    :on-remove="handleProductImageRemove"
                    :limit="1"
                  >
                    <el-button
                      :loading="isUploadBill"
                      type="primary"
                      icon="el-icon-upload"
                      >Tải lên ảnh</el-button
                    >
                  </el-upload>
                </div>

                <div class="col-lg-12 form-group">
                  <label>
                    Ảnh sản phẩm
                    <b class="text-danger">*</b>
                  </label>
                  <br />
                  <input
                    type="file"
                    accept="image/*"
                    hidden
                    id="triggerProductImageChange"
                    @change="handleProductImageChange"
                  />
                  <el-button
                    :loading="isUploadProduct"
                    type="primary mb-4"
                    @click="handleUploadProduct"
                    icon="el-icon-upload"
                  >
                    Tải lên ảnh
                  </el-button>
                  <div
                    class="card"
                    v-for="(item, index) in form.products"
                    :key="index"
                  >
                    <div class="card-body">
                      <el-button
                        type="danger"
                        circle
                        size="mini"
                        style="position: absolute; right: 10px; top: 10px"
                        icon="el-icon-close"
                        @click="handleRemoveProduct(index)"
                      />

                      <div class="card-title mb-0">
                        <h3>Sản phẩm {{ index + 1 }}</h3>
                        <p>{{ item.originalName }}</p>
                      </div>
                      <div class="row">
                        <div class="col-lg-6 col-md-6">
                          <ValidationProvider
                            rules="required"
                            name="Model"
                            v-slot="{ errors }"
                          >
                            <label>
                              Model
                              <b class="text-danger">*</b>
                            </label>
                            <el-input
                              type="text"
                              v-model="form.products[index].model"
                            />
                            <span
                              style="font-size: 12px"
                              class="text-danger"
                              v-if="errors.length"
                            >
                              {{ errors[0] }}
                            </span>
                          </ValidationProvider>
                        </div>
                        <div class="col-lg-6 col-md-6">
                          <ValidationProvider
                            rules="required"
                            name="Serial"
                            v-slot="{ errors }"
                          >
                            <label>
                              Serial
                              <b class="text-danger">*</b>
                            </label>
                            <el-input
                              type="text"
                              v-model="form.products[index].serial"
                            />
                            <span
                              style="font-size: 12px"
                              class="text-danger"
                              v-if="errors.length"
                            >
                              {{ errors[0] }}
                            </span>
                          </ValidationProvider>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-lg-12 form-group">
                  <div v-if="campainWithKplus" class="row">
                    <div class="col-lg-12 form-group">
                      <el-checkbox
                        label="Nhận quà K+"
                        size="large"
                        style="color: black !important"
                        @change="changeActiveKplus"
                      />
                    </div>
                  </div>
                </div>
                <div class="col-lg-12 form-group">
                  <div v-if="physicalGift" class="row">
                    <div class="col-lg-12 form-group">
                      <el-checkbox
                        label="Thêm địa chỉ giao quà vật lí"
                        size="large"
                        style="color: black !important"
                        @change="changeDeliveryCheck"
                      />
                    </div>
                    <div class="col-lg-6 form-group" v-if="form.deliveryCheck">
                      <ValidationProvider
                        name="Họ tên"
                        rules="required"
                        v-slot="{ errors }"
                      >
                        <label>
                          Họ tên người nhận
                          <b class="text-danger">*</b>
                        </label>
                        <el-input
                          v-model="form.delivery_name"
                          placeholder="Họ tên người nhận"
                        />
                        <span
                          class="text-danger"
                          v-if="errors.length"
                          >{{ errors[0] }}</span
                        >
                      </ValidationProvider>
                    </div>
                    <div class="col-lg-6 form-group" v-if="form.deliveryCheck">
                      <ValidationProvider
                        name="SĐT"
                        rules="required"
                        v-slot="{ errors }"
                      >
                        <label>
                          Số điện thoại
                          <b class="text-danger">*</b>
                        </label>
                        <el-input
                          v-model="form.delivery_phone"
                          placeholder="Số điện thoại người nhận"
                        />
                        <span
                          class="text-danger"
                          v-if="errors.length"
                          >{{ errors[0] }}</span
                        >
                      </ValidationProvider>
                    </div>
                    <div class="col-lg-12 form-group" v-if="form.deliveryCheck">
                      <ValidationProvider
                        name="Địa chỉ"
                        rules="required"
                        v-slot="{ errors }"
                      >
                        <label>
                          Địa chỉ
                          <b class="text-danger">*</b>
                        </label>
                        <el-input
                          v-model="form.delivery_address"
                          placeholder="Nhập đia chỉ nhận quà"
                        />
                        <span
                          class="text-danger"
                          v-if="errors.length"
                          >{{ errors[0] }}</span
                        >
                      </ValidationProvider>
                    </div>
                    <div class="col-lg-4 form-group" v-if="form.deliveryCheck">
                      <ValidationProvider
                        rules="required"
                        v-slot="{ errors }"
                        name="Tỉnh/Thành phố"
                      >
                        <div class="group-custom-input mv-26">
                          <label for="delivery_province_id">
                            Tỉnh/Thành phố
                            <span class="text-red">*</span>
                          </label>
                          <el-select
                            v-model="form.delivery_province_id"
                            clearable
                            @change="onChangeProvince"
                          >
                            <el-option
                              v-for="(province) in form.provinceList"
                              :key="province.id"
                              :value="province.id"
                              :label="province.title"
                            ></el-option>
                          </el-select>

                          <div
                            class="group-custom-input-error text-red-0000"
                            v-if="errors"
                          >
                            {{ errors[0] }}
                          </div>
                        </div>
                      </ValidationProvider>
                    </div>
                    <div class="col-lg-4 form-group" v-if="form.deliveryCheck">
                      <ValidationProvider
                        rules="required"
                        v-slot="{ errors }"
                        name="Quận/Huyện"
                      >
                        <div class="group-custom-input mv-26">
                          <label for="delivery_province_id">
                            Quận/Huyện
                            <span class="text-red">*</span>
                          </label>
                          <el-select
                            v-model="form.delivery_district_id"
                            clearable
                            @change="onChangeDistrict"
                          >
                            <el-option
                              v-for="(district) in form.districtList"
                              :key="district.id"
                              :value="district.id"
                              :label="district.title"
                            ></el-option>
                          </el-select>

                          <div
                            class="group-custom-input-error text-red-0000"
                            v-if="errors"
                          >
                            {{ errors[0] }}
                          </div>
                        </div>
                      </ValidationProvider>
                    </div>
                    <div class="col-lg-4 form-group" v-if="form.deliveryCheck">
                      <ValidationProvider
                        rules="required"
                        v-slot="{ errors }"
                        name="Phường/Xã"
                      >
                        <div class="group-custom-input mv-26">
                          <label for="delivery_province_id">
                            Phường/Xã
                            <span class="text-red">*</span>
                          </label>
                          <el-select v-model="form.delivery_ward_id" clearable>
                            <el-option
                              v-for="(ward) in form.wardList"
                              :key="ward.id"
                              :value="ward.id"
                              :label="ward.title"
                            ></el-option>
                          </el-select>

                          <div
                            class="group-custom-input-error text-red-0000"
                            v-if="errors"
                          >
                            {{ errors[0] }}
                          </div>
                        </div>
                      </ValidationProvider>
                    </div>
                  </div>
                </div>
              </div>

              <div class="d-flex justify-content-end">
                <!--              <el-button>Huỷ</el-button>-->
                <el-button
                  :loading="isLoading"
                  type="primary"
                  :disabled="invalid || !form.products.length || !form.orderImageName || isLoading"
                  @click.prevent="handleSubmit"
                >
                  Lưu
                </el-button>
              </div>
            </div>
          </div>
        </ValidationObserver>
      </el-card>
    </div>
  </div>
</template>

<script>
import { computed, defineComponent, ref, useContext, useRouter, useStore,useFetch } from '@nuxtjs/composition-api';
import { Card, Form, FormItem, Input, Button, DatePicker, Upload, Message } from 'element-ui';
import { getApiErrorMessage } from '@/util/functions.js';
import useImage from '@/composition/useImage.js';
import { clientApi } from '@/util/client-api.js';
import { useClaims, useDeliveryAddress,createClaimV2} from '@/composition';
import ConfigCampaign from '../../configs/campaign.json';

export default defineComponent({
  name: 'ClaimCreate',
  layout: 'DashboardLayout',
  components: {
    [Form.name]: Form,
    [FormItem.name]: FormItem,
    [Input.name]: Input,
    [Card.name]: Card,
    [Button.name]: Button,
    [DatePicker.name]: DatePicker,
    [Upload.name]: Upload,
  },
  setup() {

    // const store = useStore();
    const { store, $permissions } = useContext();
    const router = useRouter();
    const { $config } = useContext();
    const { id: campaignId } = store.getters["campaignStore/currentCampaign"];
    var claimExcelFile = null;
    let campainWithKplus, physicalGift;
    try {
      campainWithKplus = ConfigCampaign[campaignId]['active_kplus'] === "true";
    } catch {
      campainWithKplus = ConfigCampaign['0']['active_kplus'] === "true";
    }

    try {
      physicalGift = ConfigCampaign[campaignId]['physical_gift'] === "true";
    } catch {
      physicalGift = ConfigCampaign['0']['physical_gift'] === "true";
    }

    const { fetchDeliveryAddress,fetchProvince,fetchDistrict,fetchWard ,doSaveDeliveryAddress} = useDeliveryAddress();
    if (!$permissions.canCreateClaim()) {
      router.push('/claims');
    }



    const { uploadImage } = useImage();
    const { createClaim ,createClaimV2, importExcelAdminCreateClaim } = useClaims();
    const isLoading = ref(false);
    const isUploadProduct = ref(false);
    const isUploadBill = ref(false);

    const form = ref({
      name: '',
      phone: null,
      email: '',
      identification: '',
      orderDate: null,
      orderImageName: '',
      orderImageUrl: '',
      products: [],
      provinceList:{},
      districtList:{},
      wardList:{},
      delivery_province_id:'',
      delivery_district_id:'',
      delivery_ward_id:'',
      deliveryCheck:false,
      delivery_phone:'',
      delivery_name:'',
      delivery_address:'',
      active_k_plus:false
    });

    useFetch(async () => {
       const provinces = await fetchProvince();
       form.value.provinceList = provinces;
      });
    const orderImageList = ref([]);
    const productImageList = ref([]);

    async function handleOrderImageChange(file) {
      try {
        isUploadBill.value = true;
        const url = $config.imageUrlUpload.replace('{fileName}', file.raw.name);
        const { data } = await clientApi.post(url, file.raw, {
          headers: {
            'Content-Type': file.type,
          },
        });
        form.value.orderImageName = data.filename;
        form.value.orderImageUrl = data.url;
        isUploadBill.value = false;
      } catch (e) {
        orderImageList.value = [];
        isUploadBill.value = false;
        Message({ type: 'error', message: getApiErrorMessage(e.response.data.error.code), duration: 5000 });
      }
    }

    function handleProductImageRemove() {
      form.value.orderImageName = '';
      form.value.orderImageUrl = '';
    }

    async function handleProductImageChange(event) {
      const file = event.target.files[0];
      document.getElementById('triggerProductImageChange').value = null;
      try {
        isUploadProduct.value = true;
        const { data } = await uploadImage(file);
        form.value.products.push({
          ...data,
          originalName: file.name,
          serial: '',
          model: '',
        });
        isUploadProduct.value = false;
      } catch (e) {
        Message({ type: 'error', message: getApiErrorMessage(e.response.data.error.code), duration: 5000 });
        isUploadProduct.value = false;
      }
    }

    function handleUploadProduct() {
      document.getElementById('triggerProductImageChange').click();
    }

    async function handleSubmit() {
      try {
        isLoading.value = true;
        let phone = form.value.phone.toString();
        if (!phone.startsWith('84')) {
          if (phone.startsWith('0')) {
            phone = phone.replace('0', '84');
          } else {
            phone = '84' + phone;
          }
        }

        const payload = {
          orderStoreId: null,
          orderId: '',
          orderDate: Math.floor(new Date(form.value.orderDate).getTime() / 1000),
          orderImage: form.value.orderImageName,
          note: '',
          ip: '',
          customerName: form.value.name.trim(),
          customerPhone: phone,
          identification: form.value.identification ? form.value.identification.trim() : null,
          customerEmail: form.value.email ? form.value.email.trim() : null,
          products: form.value.products.map((product) => ({
            serial: product.serial,
            model: product.model,
            image: product.filename,
          })),
        };
        let newClaim;
        if(physicalGift || campainWithKplus){
          const param = {
            delivery_name: form.value.delivery_name,
            delivery_phone: form.value.delivery_phone,
            delivery_address: form.value.delivery_address,
            delivery_province_id: form.value.delivery_province_id,
            delivery_ward_id: form.value.delivery_ward_id,
            delivery_district_id: form.value.delivery_district_id,
            active_k_plus: form.value.active_k_plus?2:1,
          }
          newClaim = await createClaimV2(Object.assign(payload, form.value.delivery_province_id?param:{active_k_plus: form.value.active_k_plus?2:1}));
        }else{
          newClaim = await createClaim(payload);

        }

        if(newClaim?.data){
           Message({
          message: 'Thêm mới đơn hàng thành cônng',
          type: 'success',
          duration: 5000,
        });
        router.push('/claims');
        }

        isLoading.value = false;
      } catch (e) {
        isLoading.value = false;
        Message({ type: 'error', message: getApiErrorMessage(e.data.error.code), duration: 5000 });
      }
    }

    function handleRemoveProduct(index) {
      form.value.products.splice(index, 1);
    }

    async function onChangeProvince() {
        const districts = await fetchDistrict(form.value.delivery_province_id);
        form.value.districtList = districts;
        form.value.delivery_district_id = '';
        form.value.wardList = {};
        form.value.delivery_ward_id = '';
      }

      async function onChangeDistrict() {
        const wards = await fetchWard(form.value.delivery_province_id,form.value.delivery_district_id);
        form.value.wardList = wards;
        form.value.delivery_ward_id = '';
      }
      function changeDeliveryCheck(){
        form.value.deliveryCheck = !form.value.deliveryCheck;
      }
      function changeActiveKplus(){
        form.value.active_k_plus = !form.value.active_k_plus
      }

    const uploadFile = async (event) => {
      console.log(event.target.files)
      claimExcelFile = event.target.files[0];
      console.log(claimExcelFile)
    };
    const submitFile = async (e) => {
      e.preventDefault()
      await store.dispatch('setLoading', true);
      console.log(claimExcelFile)
      if (claimExcelFile != null) {
        console.log("Serial file not null")
        let data = await importExcelAdminCreateClaim(claimExcelFile);
        console.log(data)
        if (data?.code === 200) {
          Message({
            message: data.msg,
            type: 'success',
            duration: 5000,
          });
        }
      } else {
        Message({
          message: "File không được để trống",
          type: 'error',
          duration: 5000,
        });
      }
      await store.dispatch('setLoading', false);
    };

    return {
      form,
      orderImageList,
      productImageList,
      handleOrderImageChange,
      handleProductImageChange,
      handleUploadProduct,
      handleProductImageRemove,
      handleSubmit,
      handleRemoveProduct,
      isLoading,
      isUploadProduct,
      isUploadBill,
      campaignId,
      onChangeProvince,
      onChangeDistrict,
      changeDeliveryCheck,
      changeActiveKplus,
      campainWithKplus,
      physicalGift,
      uploadFile,
      submitFile,
    };
  },
});
</script>

<style scoped>
span {
  font-size: 14px;
}
</style>

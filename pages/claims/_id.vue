<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12 py-3">
          <h1 class="mb-0">Chi tiết đơn hàng</h1>
        </div>
      </div>
      <div class="row" v-if="claim">
        <ClaimDetailGeneral :claim="claim" :user="user" :initData="initData" />
        <div class="col-lg-6" v-bind:key="item.id" v-for="item in claim.claimDetail" v-show="item.gifts">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title mb-0">Thông tin chi tiết quà tặng {{ item.itemDisplay }}</h3>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6 mb-4">
                  <div class="form-group">
                    <label class="form-label">Model</label>
                    <el-input disabled placeholder="Nhập model" class="el-form-item mb-0" :value="item.model" :class="{
                      'is-unset': !stateClaimDetails[item.id].hasClaimOcr,
                      'is-error': !stateClaimDetails[item.id].isModelFieldValidity,
                      'is-success': stateClaimDetails[item.id].isModelFieldValidity,
                    }"></el-input>
                    <!-- <div
                      class="text-xs ml-1 mb-2"
                      :class="{
                        'text-danger mt-1': !stateClaimDetails[item.id].isModelProductValidity,
                        'text-success mt-1': stateClaimDetails[item.id].isModelProductValidity,
                      }"
                      v-if="getClaimDetailTracking(item.id)?.stickerModelTracking"
                    >
                      <el-button 
                        circle 
                        type="primary" 
                        icon="el-icon-success"
                        size="mini"
                        v-if="!stateClaimDetails[item.id].isModelProductValidity"
                        @click="handleUpdateOcrStickerData('stickerModelTracking', item.id, claim.id)"
                        v-loading="loadingState"
                      ></el-button>
                      <i>
                        <b class="mb-0">Sản phẩm: </b>
                        <b>({{ getClaimDetailTracking(item.id)?.stickerModelTracking?.message }})</b>
                        <div :style="{fontSize: '0.8em'}">
                          {{ `${getClaimDetailTracking(item.id)?.stickerModelTracking?.pattern}` }}
                        </div>
                      </i>
                      
                    </div> -->
                    <div class="text-xs ml-1" :class="{
                      'text-danger mt-1': !stateClaimDetails[item.id].isModelInvoiceValidity,
                      'text-success mt-1': stateClaimDetails[item.id].isModelInvoiceValidity,
                    }" v-if="getClaimDetailTracking(item.id)?.modelTracking">
                      <el-button circle type="primary" icon="el-icon-caret-bottom" size="mini" v-if="
                        (($permissions.canChangeOcr() && getClaimDetailTracking(item.id)?.modelTracking?.manual > 0) || !stateClaimDetails[item.id].isModelInvoiceValidity) &&
                        currentDropdownOpened !== 'modelTracking'
                      " @click="handleShowOcrDropdown('modelTracking', item.id)"></el-button>
                      <el-select v-if="currentDropdownOpened === 'modelTracking' && currentDropdownId == item.id"
                        @change="(value) => onOcrDropdownChange(value, 'modelTracking', item.id, claim.id)"
                        v-model="ocrDropdownValue" v-loading="loadingState" class="mb-1">
                        <el-option v-for="option in ocrDropdownOptions.slice(0, -1)" :key="option.VALUE"
                          :value="option.VALUE" :label="option.TEXT"></el-option>
                        <el-option :key="ocrDropdownOptions[3].VALUE" :value="ocrDropdownOptions[3].VALUE"
                          :label="ocrDropdownOptions[3].TEXT"></el-option>
                      </el-select>
                      <div v-if="noteVisible['modelTracking'] && currentDropdownId == item.id">
                        <el-input type="textarea" rows="4" v-model="currentOcrNoteValue['modelTracking']"
                          placeholder="Nhập ghi chú" class="mb-1"></el-input>
                        <div class="d-flex">
                          <el-button type="primary" @click="handleSubmitOcrNote('modelTracking', item.id, claim.id)">
                            Nhập ghi chú
                          </el-button>
                          <el-button type="info" @click="handleCancelNote('modelTracking')">
                            Huỷ
                          </el-button>
                        </div>

                      </div>
                      <i>
                        <b class="mb-0">Hoá đơn:</b>
                        <b>({{ getClaimDetailTracking(item.id)?.modelTracking?.message }})</b>
                        <div :style="{ fontSize: '0.8em' }">
                          {{ `${getClaimDetailTracking(item.id)?.modelTracking?.pattern}` }}
                        </div>
                        <i v-if="getClaimDetailTracking(item.id)?.modelTracking?.note">
                          Note: {{ `${getClaimDetailTracking(item.id)?.modelTracking?.note}` }}
                        </i>
                      </i>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="form-label">Serial</label>
                    <el-input disabled placeholder="Nhập serial" :value="item.serial" class="el-form-item mb-0" :class="{
                      'is-unset': !stateClaimDetails[item.id].hasClaimOcr,
                      'is-error': !stateClaimDetails[item.id].isSerialFieldValidity,
                      'is-success': stateClaimDetails[item.id].isSerialFieldValidity,
                    }"></el-input>
                    <!-- <div
                      class="text-xs ml-1"
                      :class="{
                        'text-danger mt-1': !stateClaimDetails[item.id].isSerialProductValidity,
                        'text-success mt-1': stateClaimDetails[item.id].isSerialProductValidity,
                      }"
                      v-if="getClaimDetailTracking(item.id)?.stickerSerialTracking"
                    >
                      <el-button 
                        circle 
                        type="primary" 
                        icon="el-icon-success"
                        size="mini"
                        v-if="!stateClaimDetails[item.id].isSerialProductValidity"
                        @click="handleUpdateOcrStickerData('stickerSerialTracking', item.id, claim.id)"
                        v-loading="loadingState"
                      ></el-button>
                      <i>
                        <b class="mb-0">Sản phẩm: </b>
                        <b>({{ getClaimDetailTracking(item.id)?.stickerSerialTracking?.message }})</b>
                        <div :style="{fontSize: '0.8em'}">
                          {{
                            getClaimDetailTracking(item.id)?.stickerSerialTracking?.pattern
                              ? `${getClaimDetailTracking(item.id)?.stickerSerialTracking?.pattern}`
                              : ''
                          }}
                        </div>
                      </i> 
                    </div> -->

                    <div class="text-xs ml-1" :class="{
                      'text-danger mt-1': !stateClaimDetails[item.id].isSerialInvoiceValidity,
                      'text-success mt-1': stateClaimDetails[item.id].isSerialInvoiceValidity,
                    }" v-if="getClaimDetailTracking(item.id)?.serialTracking">
                      <el-button circle type="primary" icon="el-icon-caret-bottom" size="mini" v-if="
                        (($permissions.canChangeOcr() && getClaimDetailTracking(item.id)?.serialTracking?.manual > 0) || !stateClaimDetails[item.id].isSerialInvoiceValidity) &&
                        currentDropdownOpened !== 'serialTracking'
                      " @click="handleShowOcrDropdown('serialTracking', item.id)"></el-button>
                      <el-select v-if="currentDropdownOpened === 'serialTracking' && currentDropdownId == item.id"
                        @change="(value) => onOcrDropdownChange(value, 'serialTracking', item.id, claim.id)"
                        v-model="ocrDropdownValue" v-loading="loadingState" class="mb-1">
                        <el-option v-for="option in ocrDropdownOptions.slice(0, -1)" :key="option.VALUE"
                          :value="option.VALUE" :label="option.TEXT"></el-option>
                        <el-option :key="ocrDropdownOptions[3].VALUE" :value="ocrDropdownOptions[3].VALUE"
                          :label="ocrDropdownOptions[3].TEXT"></el-option>
                      </el-select>
                      <div v-if="noteVisible['serialTracking'] && currentDropdownId == item.id">
                        <el-input type="textarea" rows="4" v-model="currentOcrNoteValue['serialTracking']"
                          placeholder="Nhập ghi chú" class="mb-1"></el-input>
                        <div class="d-flex">
                          <el-button type="primary" @click="handleSubmitOcrNote('serialTracking', item.id, claim.id)">
                            Nhập ghi chú
                          </el-button>
                          <el-button type="info" @click="handleCancelNote('serialTracking')">
                            Huỷ
                          </el-button>
                        </div>
                      </div>

                      <i>
                        <b class="mb-0">Hoá đơn:</b>
                        <b>({{ getClaimDetailTracking(item.id)?.serialTracking?.message }})</b>
                        <div :style="{ fontSize: '0.8em' }">
                          {{ `${getClaimDetailTracking(item.id)?.serialTracking?.pattern}` }}
                        </div>
                        <i v-if="getClaimDetailTracking(item.id)?.serialTracking?.note">
                          Note: {{ getClaimDetailTracking(item.id)?.serialTracking?.note }}
                        </i>
                      </i>
                    </div>
                  </div>
                </div>

                <div class="col-md-6 mb-4">
                  <label class="form-label">Ảnh serial/model</label>
                  <img class="w-100" :src="`${$config.baseImageUrl}${item.image}` || '/img/mockup.png'" alt="" />
                </div>

                <div class="form-group col-md-12" v-if="claimDetail[item.id]">
                  <label class="form-label">Quà được nhận</label>
                  <div v-bind:key="itemGift.claimDetailId" v-for="itemGift in item.gifts">
                    <el-select :disabled="!!claim.approveTwo" v-model="claimDetail[itemGift.parentId].giftId"
                      @change="onGiftChange" clearable class="mt-2">
                      <el-option v-for="gift in giftList" :key="gift.id" :value="gift.id"
                        :label="gift.title || gift.model"></el-option>
                    </el-select>
                  </div>
                </div>
                <div class="col-md-12">
                  <el-alert v-if="item.process === claimDetailProcess.PENDING.VALUE" type="info"
                    :title="claimDetailProcess.PENDING.TEXT" show-icon :closable="false" />
                  <el-alert v-if="item.process === claimDetailProcess.CANNOT_GET_GIFT.VALUE" type="error"
                    :title="claimDetailProcess.CANNOT_GET_GIFT.TEXT" show-icon :closable="false" />
                  <el-alert v-if="item.process === claimDetailProcess.CANNOT_SEND_SMS.VALUE" type="error"
                    :title="claimDetailProcess.CANNOT_SEND_SMS.TEXT" show-icon :closable="false" />
                  <el-alert v-if="item.process === claimDetailProcess.DATA_CORRUPT.VALUE" type="error"
                    :title="claimDetailProcess.DATA_CORRUPT.TEXT" show-icon :closable="false" />
                  <el-alert v-if="item.process === claimDetailProcess.SUCCESS.VALUE" type="success"
                    :title="claimDetailProcess.SUCCESS.TEXT" show-icon :closable="false" />
                </div>

                <div class="col-md-12">
                  <el-alert v-if="getClaimDetailTracking(item.id)?.stickerOnCarton?.result === false" type="success"
                    :title="getClaimDetailTracking(item.id)?.stickerOnCarton.message" show-icon :closable="false" />
                  <el-alert v-if="getClaimDetailTracking(item.id)?.stickerOnCarton?.result === true" type="error"
                    :title="getClaimDetailTracking(item.id)?.stickerOnCarton.message" show-icon :closable="false" />
                </div>
                <!-- <div class="col-12 col-md-5" v-if="lineItemsTrackingInfo && getClaimDetailTracking(item.id)">
                  <div class="card mt-3">
                    <div class="card-header bg-info text-white">Thông tin sản phẩm</div>
                    <div class="card-body">
                      <div class="form-group">
                        <label class="form-label">Sản phẩm: </label>
                        <span class="text-sm">{{ getClaimDetailTracking(item.id).modelTracking?.pattern || '' }}</span>
                      </div>
                      <div class="form-group">
                        <label class="form-label">Model: </label>
                        <span class="text-sm">{{
                            getClaimDetailTracking(item.id).warrantyTracking?.modelName || ''
                          }}</span>
                      </div>
                      <div class="form-group" v-if="getClaimDetailTracking(item.id).warrantyTracking?.suffix">
                        <label class="form-label">Suffix: </label>
                        <span class="text-sm">{{ getClaimDetailTracking(item.id).warrantyTracking?.suffix }}</span>
                      </div>
                      <div class="form-group">
                        <label class="form-label">Số serial: </label>
                        <span class="text-sm">{{
                            getClaimDetailTracking(item.id).warrantyTracking?.serialNumber || ''
                          }}</span>
                      </div>
                      <div class="form-group" v-if="getClaimDetailTracking(item.id).warrantyTracking?.manufactureDate">
                        <label class="form-label">Ngày sản xuất: </label>
                        <span class="text-sm">{{
                            getClaimDetailTracking(item.id).warrantyTracking?.manufactureDate
                          }}</span>
                      </div>
                    </div>
                  </div>
                </div> -->
                <div class="col-12" v-if="lineItemsTrackingInfo && getClaimDetailTracking(item.id)">
                  <div class="card mt-3">
                    <div class="card-header bg-info text-white">Thông tin Bảo hành</div>
                    <div class="card-body">
                      <div class="form-group">
                        <label class="form-label">Sản phẩm:</label>
                        <span class="text-sm">
                          {{ getClaimDetailTracking(item.id)?.warrantyTracking?.product || '' }}
                        </span>
                      </div>
                      <div class="form-group">
                        <label class="form-label">Model:</label>
                        <span class="text-sm">
                          {{ getClaimDetailTracking(item.id)?.warrantyTracking?.modelName || '' }}
                        </span>
                      </div>
                      <div class="form-group">
                        <label class="form-label">Số serial:</label>
                        <span class="text-sm">
                          {{ getClaimDetailTracking(item.id)?.warrantyTracking?.serialNumber || '' }}
                        </span>
                      </div>
                      <!-- <div class="form-group" v-if="getClaimDetailTracking(item.id)?.warrantyTracking?.suffix">
                        <label class="form-label">Suffix: </label>
                        <span class="text-sm">{{ getClaimDetailTracking(item.id)?.warrantyTracking?.suffix }}</span>
                      </div> -->
                      <!-- <div class="form-group">
                        <label class="form-label">Ngày mua trên hoá đơn: </label>
                        <span class="text-sm">{{
                            getClaimDetailTracking(item.id).warrantyTracking?.invoiceDate || ''
                          }}</span>
                      </div> -->
                      <div class="form-group">
                        <label class="form-label">Ngày mua:</label>
                        <span class="text-sm">
                          {{ getClaimDetailTracking(item.id)?.warrantyTracking?.purchaseDate || '' }}
                        </span>
                      </div>
                      <!-- <div class="form-group">
                        <label class="form-label">Thời hạn bảo hành: </label>
                        <span class="text-sm">{{
                            getClaimDetailTracking(item.id).warrantyTracking?.warrantyPeriodDate || ''
                          }}</span>
                      </div> -->
                      <div class="form-group">
                        <label class="form-label">Nơi mua:</label>
                        <span class="text-sm">
                          {{ getClaimDetailTracking(item.id)?.warrantyTracking?.purchaseLocation || '' }}
                        </span>
                      </div>

                      <!-- <div class="form-group" v-if="getClaimDetailTracking(item.id).warrantyTracking?.address">
                        <label class="form-label">Địa chỉ: </label>
                        <span class="text-sm">{{ getClaimDetailTracking(item.id).warrantyTracking?.address }}</span>
                      </div>

                      <div class="form-group" v-if="getClaimDetailTracking(item.id).warrantyTracking?.province">
                        <label class="form-label">Tỉnh/Thành phố: </label>
                        <span class="text-sm">{{ getClaimDetailTracking(item.id).warrantyTracking?.province }}</span>
                      </div>

                      <div class="form-group" v-if="getClaimDetailTracking(item.id).warrantyTracking?.note">
                        <label class="form-label">Ghi chú: </label>
                        <span class="text-sm">{{ getClaimDetailTracking(item.id).warrantyTracking?.note }}</span>
                      </div> -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-footer">
              <div class="row">
                <div class="col-md-6 mb-4 d-flex">
                  <el-button v-if="claim.process !== 3" type="success" @click="handleWarrantyInfo(claim, item.serial)">
                    Cập nhật thông tin bảo hành
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-lg-12" v-if="claim.needCheckCombo">
          <div class="card">
            <div class="card-body">
              <el-alert v-if="claim.isCouple" type="success" title="Model trùng nhau" show-icon :closable="false" />
              <el-alert v-if="!claim.isCouple" type="error" title="Model không trùng nhau" show-icon
                :closable="false" />
            </div>
          </div>
        </div>
        <div class="col-lg-12" v-if="globalParams.hasDeliveryAddress">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title mb-0">Địa chỉ nhận quà vật lý</h3>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="form-group col-lg-4">
                  <label class="form-label">Tên người nhận</label>
                  <el-input type="text" v-model="globalParams.delivery_name" placeholder="Tên người nhận"></el-input>
                </div>
                <div class="form-group col-lg-4">
                  <label class="form-label">Số điện thoại</label>
                  <el-input type="text" v-model="globalParams.delivery_phone" placeholder="Số điện thoại"></el-input>
                </div>
                <div class="form-group col-lg-4">
                  <label class="form-label">Địa chỉ</label>
                  <el-input type="text" v-model="globalParams.delivery_address" placeholder="Địa chỉ"></el-input>
                </div>
              </div>
              <div class="row">
                <div class="form-group col-lg-4">
                  <label class="form-label">Tỉnh/Thành phố</label>
                  <el-select @change="onChangeProvince" v-model="globalParams.delivery_province_id" clearable>
                    <el-option v-for="province in globalParams.provinceList" :key="province.id" :value="province.id"
                      :label="province.title"></el-option>
                  </el-select>
                </div>
                <div class="form-group col-lg-4">
                  <label class="form-label">Quận/Huyện</label>
                  <el-select @change="onChangeDistrict" v-model="globalParams.delivery_district_id" clearable>
                    <el-option v-for="district in globalParams.districtList" :key="district.id" :value="district.id"
                      :label="district.title"></el-option>
                  </el-select>
                </div>
                <div class="form-group col-lg-4">
                  <label class="form-label">Phường/Xã</label>
                  <el-select v-model="globalParams.delivery_ward_id" clearable>
                    <el-option v-for="ward in globalParams.wardList" :key="ward.id" :value="ward.id"
                      :label="ward.title"></el-option>
                  </el-select>
                </div>
                <div class="form-group col-lg-4">
                  <div>
                    <label class="form-label"></label>
                  </div>
                  <el-button v-if="$permissions.hasAnyPermission(['claims.create', 'claims.update'])" type="primary"
                    @click="updateDelivery">
                    Lưu
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-lg-12">
          <div class="card">
            <div class="card-body">
              <ValidationObserver ref="approvalForm" v-slot="{ invalid }">
                <el-alert center type="error" show-icon closable :title="errorMessage" v-if="errorMessage"
                  class="mb-4" />
                <el-alert center type="success" show-icon closable :title="successMessage" v-if="successMessage"
                  class="mb-4" />
                <div class="row">
                  <div class="form-group col-lg-4">
                    <el-dropdown trigger="click">
                      <el-button class="el-dropdown-link" :disabled="!!claim.approveTwo">
                        Gọi lần 1
                        <el-icon class="el-icon--right"></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item v-for="(label, key) in logOption" :key="key"
                            class="custom-el-dropdown-item">
                            <el-button @click="handleLogCall(label, 1)" class="custom-button">
                              {{ label }}
                            </el-button>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                    <div class="mt-3">{{ filteredClaimTypeLogs(1) }}</div>
                  </div>
                  <div class="form-group col-lg-4">
                    <el-dropdown trigger="click">
                      <el-button class="el-dropdown-link" :disabled="!showDropdown(1) || !!claim.approveTwo">
                        Gọi lần 2
                        <el-icon class="el-icon--right"></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item v-for="(label, key) in logOption" :key="key"
                            class="custom-el-dropdown-item">
                            <el-button @click="handleLogCall(label, 2)" class="custom-button">
                              {{ label }}
                            </el-button>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                    <div class="mt-3">{{ filteredClaimTypeLogs(2) }}</div>
                  </div>
                  <div class="form-group col-lg-4">
                    <el-dropdown trigger="click">
                      <el-button class="el-dropdown-link" :disabled="!showDropdown(2) || !!claim.approveTwo">
                        Gọi lần 3
                        <el-icon class="el-icon--right"></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item v-for="(label, key) in logOption" :key="key"
                            class="custom-el-dropdown-item">
                            <el-button @click="handleLogCall(label, 3)" class="custom-button">
                              {{ label }}
                            </el-button>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                    <div class="mt-3">{{ filteredClaimTypeLogs(3) }}</div>
                  </div>
                  <div class="form-group col-lg-4">
                    <el-dropdown trigger="click">
                      <el-button class="el-dropdown-link" :disabled="!!claim.approveTwo">
                        SĐT trên hóa đơn/ SĐT Kích hoạt bảo hành
                        <el-icon class="el-icon--right"></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item v-for="(label, key) in logConfirmOption" :key="key"
                            class="custom-el-dropdown-item">
                            <el-button @click="handleLogCall(label, 4)" class="custom-button">
                              {{ label }}
                            </el-button>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                    <div class="mt-3">{{ filteredClaimTypeLogs(4) }}</div>
                  </div>
                  <div class="form-group col-lg-12">
                    <div class="form-group col-lg-12">
                      <ClaimDetailLogs :claimLogs="claimLogs.data" />
                    </div>
                  </div>
                </div>
                <div class="row">

                  <div class="form-group col-lg-4">
                    <label class="form-label">Mô tả/Ghi chú</label>
                    <el-input type="textarea" rows="5" v-model="globalParams.note" placeholder="Ghi chú"></el-input>
                  </div>
                  <div class="form-group col-lg-2">
                    <div>
                      <label class="form-label">&nbsp</label>
                    </div>
                    <el-button type="primary"
                      v-if="claim.process === claimProcess.PENDING.VALUE && $permissions.hasAnyPermission(['claims.create', 'claims.update'])"
                      @click="handleNote">
                      Bắt đầu xử lý
                    </el-button>
                    <el-button type="primary"
                      v-if="claim.process !== claimProcess.PENDING.VALUE && $permissions.hasAnyPermission(['claims.create', 'claims.update'])"
                      @click="handleNote">
                      Lưu note
                    </el-button>
                  </div>
                </div>

                <div class="row">
                  <div class="form-group col-lg-12">
                    <div>Lịch sử</div>
                    <div>
                      <el-table class="table-responsive" size="mini" :highlight-current-row="true"
                        :cell-style="{ textAlign: 'center' }"
                        :header-cell-style="{ justifyContent: 'center', fontWeight: 'bold' }" :data="filteredClaimLogs"
                        style="width: 100%">
                        <el-table-column label="Tài khoản">
                          <template v-slot="{ row }">
                            {{ row.account }}
                          </template>
                        </el-table-column>
                        <el-table-column label="Ngày tạo">
                          <template v-slot="{ row }">
                            {{ row.created_at | formatDatetimeFromInt }}
                          </template>
                        </el-table-column>
                        <el-table-column label="Ghi chú">
                          <template v-slot="{ row }">
                            {{ row.note }}
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </div>
                </div>
                <!--                <div class="row">-->
                <!--                  <div class="form-group col-lg-4">-->
                <!--                    <label class="form-label">Ghi chú pending</label>-->
                <!--                    <el-input-->
                <!--                        :disabled="claim.process !== claimProcess.PENDING.VALUE"-->
                <!--                        v-model="pendingApproval.note"-->
                <!--                        placeholder="Ghi chú vì sao pending"-->
                <!--                    ></el-input>-->
                <!--                  </div>-->
                <!--                  <div class="form-group col-lg-4" v-if="claim.process !== claimProcess.PENDING.VALUE">-->
                <!--                    <label class="form-label">Người duyệt</label>-->
                <!--                    <el-input-->
                <!--                        :disabled="claim.process !== claimProcess.PENDING.VALUE"-->
                <!--                        v-model="pendingApproval.account"-->
                <!--                        placeholder="Ghi chú"-->
                <!--                    ></el-input>-->
                <!--                  </div>-->
                <!--                </div>-->

                <div class="row" v-if="claim.process !== claimProcess.PENDING.VALUE">
                  <div class="col-lg-4" v-if="!claim.approveOne">
                    <div class="form-group">
                      <label class="form-label">
                        Duyệt đợt 1
                        <b class="text-danger">*</b>
                      </label>
                      <el-select v-model="firstApproval.status" :disabled="!!claim.approveOne">
                        <el-option v-for="item in approveStatusList" :key="item.VALUE" :value="item.VALUE"
                          :label="item.TEXT"></el-option>
                      </el-select>
                    </div>
                  </div>

                  <div class="form-group col-lg-4">
                    <label class="form-label">Tài khoản duyệt đợt 1</label>
                    <el-input disabled v-model="firstApproval.account"
                      placeholder="Nhập tài khoản duyệt đợt 1"></el-input>
                  </div>

                  <div class="form-group col-lg-4" v-if="
                    (firstApproval.status === approveStatus.DENY.VALUE && !claim.approveOne) || claim.approveOneNote
                  ">
                    <ValidationProvider name="Lý do từ chối" :rules="!claim.approveOne ? 'required' : ''"
                      v-slot="{ errors }">
                      <label class="form-label">
                        Lý do từ chối
                        <b class="text-danger">*</b>
                      </label>
                      <el-input :disabled="!!claim.approveOne" :class="errors.length ? 'mb-4' : ''"
                        v-model="firstApproval.note" placeholder="Nhập lý do từ chối"></el-input>
                      <validation-error :errors="errors" />
                    </ValidationProvider>
                  </div>
                </div>

                <div class="row" v-if="claim.approveOne">
                  <div class="col-lg-4" v-if="!claim.approveTwo">
                    <div class="form-group">
                      <label class="form-label">
                        Duyệt đợt 2
                        <b class="text-danger">*</b>
                      </label>
                      <el-select v-model="secondApproval.status" :disabled="!!claim.approveTwo">
                        <el-option v-for="item in approveStatusList" :key="item.VALUE" :value="item.VALUE"
                          :label="item.TEXT"></el-option>
                      </el-select>
                    </div>
                  </div>

                  <div class="form-group col-lg-4">
                    <label class="form-label">Tài khoản duyệt đợt 2</label>
                    <el-input disabled v-model="secondApproval.account"
                      placeholder="Nhập tài khoản duyệt đợt 2"></el-input>
                  </div>

                  <div class="form-group col-lg-4" v-if="
                    (secondApproval.status === approveStatus.DENY.VALUE && !claim.approveTwo) || claim.approveTwoNote
                  ">
                    <ValidationProvider name="Lý do từ chối" rules="required" v-slot="{ errors }">
                      <label class="form-label">
                        Lý do từ chối
                        <b class="text-danger">*</b>
                      </label>
                      <el-input :disabled="!!claim.approveTwo" :class="errors.length ? 'mb-4' : ''"
                        v-model="secondApproval.note" placeholder="Nhập lý do từ chối"></el-input>
                      <validation-error :errors="errors" />
                    </ValidationProvider>
                  </div>

                  <div class="form-group col-lg-4" v-if="
                    (secondApproval.status === approveStatus.DENY.VALUE && !claim.approveTwo) || claim.approveTwoNote
                  ">
                    <el-checkbox label="từ chối gửi sms" size="large" style="" @change="changeSmsRefuseCheck" />
                  </div>
                </div>

                <div class="row">
                  <div class="col-lg-12 d-flex justify-content-end">
                    <el-button type="warning"
                      v-if="isMustResendGift && $permissions.hasAnyPermission(['claims.approve', 'claims.update'])"
                      @click="handleResendGift">
                      Gửi lại quà
                    </el-button>
                    <el-button
                      v-if="claim.process !== claimProcess.PENDING.VALUE && $permissions.hasAnyPermission(['claims.approve'])"
                      type="primary" @click="handleSubmit"
                      :disabled="!isGiftsSelected || invalid || (!!claim.approveOne && !!claim.approveTwo)">
                      Lưu
                    </el-button>
                  </div>
                </div>
              </ValidationObserver>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  computed,
  defineComponent,
  reactive,
  ref,
  useContext,
  useFetch,
  useRoute,
  useRouter,
} from '@nuxtjs/composition-api';
import {
  Alert,
  Button,
  Form,
  FormItem,
  Message,
  Option,
  Select,
  Table,
  TableColumn,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  Icon,
} from 'element-ui';

import {
  APPROVE_STATUS,
  APPROVE_STATUS_LIST,
  CLAIM_DETAIL_PROCESS,
  CLAIM_PROCESS,
  STICKER_ON_CARTON,
  OCR_DROPDOWN_OPTIONS_LIST,
} from '@/util/constant.js';
import { useApproval, useClaims, useDeliveryAddress, useGifts } from '../../composition';
import ClaimDetailGeneral from '@/components/pages/claim/detail/ClaimDetailGeneral.vue';
import LoadingPanel from '@/components/argon-core/LoadingPanel.vue';
import LoadingContent from '@/components/LoadingContent.vue';
import ClaimDetailLogs from '@/components/pages/claim/detail/ClaimDetailLogs.vue';
import { merge } from 'lodash';

export default defineComponent({
  name: 'ClaimDetail',
  layout: 'DashboardLayout',
  components: {
    LoadingContent,
    LoadingPanel,
    ClaimDetailGeneral,
    ClaimDetailLogs,
    [Form.name]: Form,
    [FormItem.name]: FormItem,
    [Option.name]: Option,
    [Select.name]: Select,
    [Button.name]: Button,
    [Alert.name]: Alert,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Dropdown.name]: Dropdown,
    [DropdownItem.name]: DropdownItem,
    [DropdownMenu.name]: DropdownMenu,
    [Icon.name]: Icon,
  },
  computed: {
    stateClaimDetails: function () {
      let mappedArray = [];
      if (this.claim?.claimDetail) {
        mappedArray = this.claim.claimDetail.map((claimDetail) => {
          return {
            id: claimDetail.id,
            hasClaimOcr: this.getClaimDetailTracking(claimDetail.id),
            isModelFieldValidity: this.checkModelFieldValidity(claimDetail.id),
            isSerialFieldValidity: this.checkSerialFieldValidity(claimDetail.id),
            isSerialProductValidity: this.checkSerialProductValidity(claimDetail.id),
            isSerialInvoiceValidity: this.checkSerialInvoiceValidity(claimDetail.id),
            isModelProductValidity: this.checkModelProductValidity(claimDetail.id),
            isModelInvoiceValidity: this.checkModelInvoiceValidity(claimDetail.id),
          };
        });
      }

      return mappedArray.reduce((acc, item) => {
        acc[item.id] = item;
        return acc;
      }, {});
    },
    filteredClaimLogs() {
      // Lọc dữ liệu chỉ giữ lại những hàng có type không xác định hoặc nhỏ hơn 1
      const claimLog = this.claimLogs.data.filter(row => !row.type || row.type < 1);
      return claimLog.sort((a, b) => a.created_at - b.created_at);
    },
  },
  methods: {
    checkModelFieldValidity(id) {
      const claimDetailTracking = this.getClaimDetailTracking(id);
      if (claimDetailTracking?.stickerModelTracking) {
        return this.checkModelProductValidity(id) && this.checkModelInvoiceValidity(id);
      } else {
        return this.checkModelInvoiceValidity(id);
      }

    },
    checkModelProductValidity(id) {
      const claimDetailTracking = this.getClaimDetailTracking(id);
      return (
        claimDetailTracking?.stickerModelTracking?.result === true ||
        claimDetailTracking?.stickerModelTracking?.manual > 0
      );
    },
    checkModelInvoiceValidity(id) {
      const claimDetailTracking = this.getClaimDetailTracking(id);
      return (claimDetailTracking?.modelTracking?.result === true || claimDetailTracking?.modelTracking?.manual > 0);
    },
    checkSerialFieldValidity(id) {
      const claimDetailTracking = this.getClaimDetailTracking(id);
      if (claimDetailTracking?.stickerSerialTracking) {
        return this.checkSerialProductValidity(id) && this.checkSerialInvoiceValidity(id);
      } else {
        return this.checkSerialInvoiceValidity(id);
      }
    },

    checkSerialProductValidity(id) {
      const claimDetailTracking = this.getClaimDetailTracking(id);
      return (
        claimDetailTracking?.stickerSerialTracking?.result === true ||
        claimDetailTracking?.stickerSerialTracking?.manual > 0
      );
    },
    checkSerialInvoiceValidity(id) {
      const claimDetailTracking = this.getClaimDetailTracking(id);
      return claimDetailTracking?.serialTracking?.result === true || claimDetailTracking?.serialTracking?.manual > 0;
    },
    showDropdown(type) {
      return this.claimLogs.data.some(log => log.type === type);
    },
    filteredClaimTypeLogs(type) {
      // Lọc và sắp xếp đồng thời các logs có type tương ứng
      return this.claimLogs.data
        .filter(row => row.type === type)
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0]?.note || null;
    },
  },
  setup() {
    // const store = useStore();
    const { store } = useContext();
    const route = useRoute();
    const router = useRouter();
    const claimId = route.value.params.id;

    const loadingState = ref(false);
    const claim = ref(null);
    const errorMessage = ref('');
    const successMessage = ref('');
    const isGiftsSelected = ref(true);
    const isMustResendGift = ref(false);
    const smsRefuseCheck = ref(false);

    const currentDropdownOpened = ref('');
    const currentDropdownId = ref('');
    const ocrDropdownValue = ref('');
    const noteVisible = ref({ serialTracking: false, modelTracking: false });
    const currentOcrNoteValue = ref({ modelTracking: '', serialTracking: '' });
    const logOption = {
      1: 'Gọi duyệt thành công',
      2: 'Gọi từ chối thành công',
      3: 'Không nghe máy',
      4: 'Thuê bao',
      5: 'Gọi lại sau',
      6: 'Không xác nhận đổi quà',
      7: 'Sai SĐT',
    };

    const logConfirmOption = {
      1: 'Đồng ý uỷ quyền',
      2: 'Không đồng ý uỷ quyền',
      3: 'GL1 không thành công',
      4: 'GL2 không thành công',
      5: 'GL3 không thành công',
    };

    const { resentGift, fetchDetailClaim, updateOcrStickerData, updateWarrantyInfo } = useClaims();
    const { fetchDeliveryAddress, fetchProvince, fetchDistrict, fetchWard, doSaveDeliveryAddress } =
      useDeliveryAddress();
    const { fetchGiftList } = useGifts();
    const { doFirstApproval, doSecondApproval, doSaveNote, fetchNoteLog } = useApproval();

    const giftList = computed(() => {
      return store.getters['gift/list'];
    });

    const user = computed(() => {
      return store.getters['loggedInUser'];
    });

    const claimDetail = ref({});
    const isFirstApproval = ref(true);
    const claimLogs = ref({});

    const globalParams = reactive({
      note: '',
      delivery_name: '',
      delivery_phone: '',
      delivery_address: '',
      delivery_province_id: '',
      delivery_district_id: '',
      delivery_ward_id: '',
      provinceList: {},
      districtList: {},
      wardList: {},
      hasDeliveryAddress: false,
    });

    const pendingApproval = reactive({
      status: 1,
      note: '',
      account: '',
    });

    const firstApproval = reactive({
      status: 1,
      note: '',
      account: '',
    });

    const secondApproval = reactive({
      status: 1,
      note: '',
      account: '',
    });

    const initData = async () => {
      try {
        await fetchGiftList();
        const data = await fetchDetailClaim(claimId);
        const deliveryAddress = await fetchDeliveryAddress(claimId);
        const claimLogDatas = await fetchNoteLog(claimId);
        if (claimLogs) {
          claimLogs.value = claimLogDatas;
        }

        if (deliveryAddress && deliveryAddress.provinceId) {
          globalParams.hasDeliveryAddress = true;
          const provinces = await fetchProvince();
          globalParams.delivery_name = deliveryAddress.name;
          globalParams.delivery_phone = deliveryAddress.phone;
          globalParams.delivery_address = deliveryAddress.address;
          globalParams.provinceList = provinces;
          globalParams.delivery_province_id = deliveryAddress.provinceId;
          globalParams.delivery_district_id = deliveryAddress.districtId;
          globalParams.delivery_ward_id = deliveryAddress.wardId;

          const districts = await fetchDistrict(deliveryAddress.provinceId);
          globalParams.districtList = districts;
          const wards = await fetchWard(deliveryAddress.provinceId, deliveryAddress.districtId);
          globalParams.wardList = wards;
        }
        if (data) {
          const tempData = {};
          let serial = '';
          let indexGroup = 0;
          let itemDisplay = 0;
          for (let i in data.claimDetail) {
            tempData[data.claimDetail[i].id] = {
              claimDetailId: data.claimDetail[i].id,
              giftId: Object.keys(data.claimDetail[i].gift).length
                ? data.claimDetail[i].gift.giftId
                : data.claimDetail[i].suggest.giftId,
            };
            data.claimDetail[i].gift.parentId = data.claimDetail[i].id;
            if (data.claimDetail[i].serial != serial) {
              itemDisplay++;
              data.claimDetail[i].itemDisplay = itemDisplay;
              data.claimDetail[i].gifts = [data.claimDetail[i].gift];
              indexGroup = i;
            } else {
              data.claimDetail[indexGroup].gifts.push(data.claimDetail[i].gift);
            }
            serial = data.claimDetail[i].serial;
          }

          claim.value = data;
          lineItemsTracking(data);
          for (const item of data.claimDetail) {
            if (
              item.process === CLAIM_DETAIL_PROCESS.DATA_CORRUPT.VALUE ||
              item.process === CLAIM_DETAIL_PROCESS.CANNOT_SEND_SMS.VALUE ||
              item.process === CLAIM_DETAIL_PROCESS.CANNOT_GET_GIFT.VALUE
            ) {
              isMustResendGift.value = true;
              break;
            }
          }

          claimDetail.value = tempData;
          globalParams.note = data.note;

          onGiftChange(null);
          if (data.approveOne) {
            firstApproval.account = data.approveOne.email;
            firstApproval.note = data.approveOneNote || '';
            firstApproval.status =
              data.process === CLAIM_PROCESS.DENIED_1.VALUE || data.process === CLAIM_PROCESS.DENIED_2.VALUE
                ? APPROVE_STATUS.DENY.VALUE
                : APPROVE_STATUS.APPROVAL.VALUE;
          } else {
            firstApproval.account = user.value.email;
          }

          if (data && data.approvePending) {
            pendingApproval.account = data.approvePending.email;
            pendingApproval.note = data.approvePendingNote || '';
            pendingApproval.status =
              data.process === CLAIM_PROCESS.DENIED_1.VALUE || data.process === CLAIM_PROCESS.DENIED_2.VALUE
                ? APPROVE_STATUS.DENY.VALUE
                : APPROVE_STATUS.APPROVAL.VALUE;
          } else {
            pendingApproval.account = user.value.email;
          }

          if (data && data.approveTwo) {
            secondApproval.account = data.approveTwo.email;
            secondApproval.note = data.approveTwoNote || '';
            secondApproval.status =
              data.process === CLAIM_PROCESS.DENIED_1.VALUE || data.process === CLAIM_PROCESS.DENIED_2.VALUE
                ? APPROVE_STATUS.DENY.VALUE
                : APPROVE_STATUS.APPROVAL.VALUE;
          } else {
            secondApproval.account = user.value.email;
          }
        } else {
          router.push('/claims');
        }
      } catch (e) {
        Message({
          message: JSON.stringify(e.data.error.message),
          type: 'error',
          duration: 5 * 1000,
        });
        router.push('/claims');
      }
    };

    useFetch(async () => {
      await store.dispatch('setLoading', true);
      await initData();
      await store.dispatch('setLoading', false);
    });

    async function handleWarrantyInfo(claim, serial) {
      await store.dispatch('setLoading', true);
      const warranty = await updateWarrantyInfo({
        id: claim.id, customer_id: claim.customer.id, order_date: claim.orderDate, serial,
      });
      if (!warranty?.success) {
        await store.dispatch('setLoading', false);
      } else {
        window.location.reload();
      }
    }

    async function handleNote() {
      try {
        errorMessage.value = '';
        successMessage.value = '';

        await store.dispatch('setLoading', true);

        await doSaveNote(claim.value, globalParams.note, user.value.email, 0);
        successMessage.value = 'Note saved';
        await initData();
      } catch (e) {
        if (e.data && e.data.message) {
          errorMessage.value = e.data.message;
        }
      } finally {
        await store.dispatch('setLoading', false);
      }
    }

    async function handleLogCall(note, type) {
      try {
        errorMessage.value = '';
        successMessage.value = '';

        await store.dispatch('setLoading', true);

        await doSaveNote(claim.value, note, user.value.email, type);
        successMessage.value = 'Note saved';
        await initData();
      } catch (e) {
        if (e.data && e.data.message) {
          errorMessage.value = e.data.message;
        }
      } finally {
        await store.dispatch('setLoading', false);
      }
    }

    async function updateDelivery() {
      try {
        errorMessage.value = '';
        successMessage.value = '';

        await store.dispatch('setLoading', true);
        const param = {
          name: globalParams.delivery_name,
          phone: globalParams.delivery_phone,
          address: globalParams.delivery_address,
          province_id: globalParams.delivery_province_id,
          ward_id: globalParams.delivery_ward_id,
          district_id: globalParams.delivery_district_id,
          claim_id: Number(claimId),
        };
        await doSaveDeliveryAddress(param);
        successMessage.value = 'Delivery address saved';
        await initData();
      } catch (e) {
        if (e.data && e.data.message) {
          errorMessage.value = e.data.message;
        }
      } finally {
        await store.dispatch('setLoading', false);
      }
    }

    async function handleSubmit() {
      try {
        await store.dispatch('setLoading', true);
        errorMessage.value = '';
        successMessage.value = '';

        firstApproval.note = firstApproval.note.trim();
        secondApproval.note = secondApproval.note.trim();
        pendingApproval.note = pendingApproval.note.trim();

        if (!claim.value.approveOne) {
          if (firstApproval.status === APPROVE_STATUS.DENY.VALUE && !firstApproval.note) {
            return;
          }
          await doFirstApproval(claim.value, firstApproval, claimDetail.value, globalParams.note);
          successMessage.value =
            firstApproval.status === APPROVE_STATUS.APPROVAL.VALUE
              ? 'Duyệt lần 1 thành công'
              : 'Từ chối lần 1 thành công';
        } else {
          if (secondApproval.status === APPROVE_STATUS.DENY.VALUE && !secondApproval.note) {
            return;
          }
          let sms_refuse = 1;
          if (secondApproval.status === APPROVE_STATUS.DENY.VALUE) {
            if (smsRefuseCheck.value == false) {
              sms_refuse = 1;
            } else if (smsRefuseCheck.value == true) {
              sms_refuse = 2;
            }
          }

          await doSecondApproval(claim.value, secondApproval, claimDetail.value, globalParams.note, sms_refuse);
          successMessage.value =
            secondApproval.status === APPROVE_STATUS.APPROVAL.VALUE
              ? 'Duyệt lần 2 thành công'
              : 'Từ chối lần 2 thành công';
        }
        await initData();
      } catch (e) {
        if (e.data && e.data.message) {
          errorMessage.value = e.data.message;
        }
      } finally {
        await store.dispatch('setLoading', false);
      }
    }

    async function handleResendGift() {
      await store.dispatch('setLoading', true);
      await resentGift(claim.value.id);
      await initData();
      await store.dispatch('setLoading', false);
    }

    const claimProcess = CLAIM_DETAIL_PROCESS;
    const approveStatus = APPROVE_STATUS;
    const approveStatusList = APPROVE_STATUS_LIST;
    const ocrDropdownOptions = OCR_DROPDOWN_OPTIONS_LIST;

    function onGiftChange(_) {
      Object.keys(claimDetail.value).forEach((key) => {
        isGiftsSelected.value = !!claimDetail.value[key].giftId;
      });
    }

    function onOcrDropdownChange(selectedValue, field, itemId, claimId) {
      if (selectedValue !== ocrDropdownOptions[3].VALUE) {
        handleUpdateOcrStickerData(field, itemId, claimId, selectedValue, ocrDropdownOptions.find(option => option.VALUE === selectedValue).TEXT);
        currentDropdownOpened.value = '';
        currentDropdownId.value = '';
        noteVisible.value[field] = false;
      } else {
        noteVisible.value[field] = true;
      }
    }

    function handleSubmitOcrNote(field, itemId, claimId) {
      handleUpdateOcrStickerData(
        field,
        itemId,
        claimId,
        ocrDropdownOptions[3].VALUE,
        currentOcrNoteValue.value[field],
      );
      currentDropdownOpened.value = '';
      currentDropdownId.value = '';
      noteVisible.value[field] = false;
    }

    function handleCancelNote(field) {
      currentDropdownOpened.value = '';
      noteVisible.value[field] = false;
    }

    async function onChangeProvince(_) {
      const districts = await fetchDistrict(globalParams.delivery_province_id);
      globalParams.districtList = districts;
      globalParams.delivery_district_id = '';
      globalParams.wardList = {};
      globalParams.delivery_ward_id = '';
    }

    function changeSmsRefuseCheck() {
      smsRefuseCheck.value = !smsRefuseCheck.value;
    }

    async function onChangeDistrict(_) {
      const wards = await fetchWard(globalParams.delivery_province_id, globalParams.delivery_district_id);
      globalParams.wardList = wards;
      globalParams.delivery_ward_id = '';
    }

    const lineItemsTrackingInfo = ref(null);

    const lineItemsTracking = (claim) => {
      let _lineItemsTracking = claim?.claimOcr?.metaData?.lineItemsTracking || null;
      console.log('_lineItemsTracking', _lineItemsTracking);
      if (_lineItemsTracking) {
        const lineItemsTrackingList = {};
        _lineItemsTracking.forEach((item) => {
          item.modelTracking = {
            message: '',
            pattern: '',
            original: '',
            result: undefined,
            manual: 0,
            ...(item.modelTracking || {}),
          };

          let invoiceDate = '';
          if (item.warrantyTracking) {
            if (item.warrantyTracking?.invoiceDateAt) {
              const timestampToDate = new Date(item.warrantyTracking?.invoiceDateAt * 1000);
              invoiceDate = timestampToDate.toLocaleDateString('vi-VN').toString();
            }
            item.warrantyTracking.invoiceDate = invoiceDate;
            // item.warrantyTracking = {
            // message: '',
            //   pattern: '',
            //   original: '',
            //   serialNumber: '',
            //   warrantyPeriodDate: '',
            //   warrantyPeriodTime: 0,
            //   result: undefined,
            //   manual: 0,
            //   invoiceDate: 0,
            //   ...(item.warrantyTracking || {}),
            //  };
            merge(lineItemsTrackingList, { [item.warrantyTracking.refId]: item });
          }
        });

        lineItemsTrackingInfo.value = lineItemsTrackingList;
      }
    };
    const getClaimDetailTracking = (claimDetailId) => {
      if (!lineItemsTrackingInfo.value || typeof lineItemsTrackingInfo.value[claimDetailId] === 'undefined')
        return null;
      return lineItemsTrackingInfo.value[claimDetailId];
    };

    const handleUpdateOcrStickerData = async (field, ref_id, claim_id, value, note) => {
      loadingState.value = true;
      const res = await updateOcrStickerData({
        claimId: claim_id,
        field: field,
        value: value,
        ref_id: ref_id,
        adminId: user.id,
        note: note,
      });
      if (res) {
        await initData();
      }
      setTimeout(() => {
        loadingState.value = false;
      }, 4000);
    };

    const handleShowOcrDropdown = (field, id) => {
      currentDropdownOpened.value = field;
      currentDropdownId.value = id;
      ocrDropdownValue.value = '';
    };

    return {
      claimDetailProcess: CLAIM_DETAIL_PROCESS,
      approveStatus,
      approveStatusList,
      claimProcess,
      claim,
      giftList,
      firstApproval,
      secondApproval,
      pendingApproval,
      globalParams,
      handleSubmit,
      claimDetail,
      isFirstApproval,
      errorMessage,
      successMessage,
      isGiftsSelected,
      onGiftChange,
      isMustResendGift,
      handleResendGift,
      handleNote,
      onChangeProvince,
      onChangeDistrict,
      updateDelivery,
      changeSmsRefuseCheck,
      getClaimDetailTracking,
      lineItemsTrackingInfo,
      handleUpdateOcrStickerData,
      handleShowOcrDropdown,
      ocrDropdownOptions,
      ocrDropdownValue,
      onOcrDropdownChange,
      handleSubmitOcrNote,
      handleCancelNote,
      noteVisible,
      currentOcrNoteValue,
      claimLogs,
      user,
      loadingState,
      currentDropdownOpened,
      currentDropdownId,
      initData,
      logOption,
      handleLogCall,
      logConfirmOption,
      handleWarrantyInfo,
    };
  },
});
</script>

<style>
.form-label {
  font-size: 14px;
  font-weight: 600;
}

.form-group {
  margin-bottom: 1rem;
}

.el-input.is-disabled {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.el-input.is-unset .el-input__inner {
  border-color: #e4e7ed !important;
}

.el-select .el-input .el-input__inner:disabled {
  background-color: #f5f7fa;
  opacity: 1;
}

li.el-select-dropdown__item {
  white-space: normal;
  word-wrap: break-word;
  height: fit-content;
}

li.el-select-dropdown__item:nth-child(even) {
  background-color: rgb(245, 245, 245);
}

li.el-select-dropdown__item:nth-child(even):hover {
  background-color: rgb(248, 248, 248);
}

ul.el-select-dropdown__list {
  max-width: 60vw;
}

.el-table .cell {
  justify-content: center;
}

.el-dropdown-link {
  color: #FFF;
  background-color: #409EFF;
  border-color: #409EFF;
}

.custom-button {
  border: 0px;
  background-color: transparent;
  width: 100%;
  text-align: left;
}

.custom-button:hover {
  background-color: transparent;
}

.custom-el-dropdown-item {
  padding: 0px;
}
</style>

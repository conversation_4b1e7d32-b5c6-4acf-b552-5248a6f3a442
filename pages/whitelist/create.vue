<template>
  <div>
    <div class="container-fluid mt-4">
      <el-card shadow="always" class="box-card">
        <div slot="header" class="clearfix">
          <span>Tạo mới claim</span>
        </div>
        <ValidationObserver v-slot="{ invalid }">
          <div class="row justify-content-center">
            <div class="col-lg-8">
              <div class="row">
                <div class="col-lg-12 form-group">
                  <ValidationProvider name="Họ tên khách hàng" rules="required" v-slot="{ errors }">
                    <label>
                      Họ tên khách hàng
                      <b class="text-danger">*</b>
                    </label>
                    <el-input v-model="form.name" placeholder="Nhập họ và tên khách hàng" />
                    <span class="text-danger" v-if="errors.length">{{ errors[0] }}</span>
                  </ValidationProvider>
                </div>

                <div class="form-group col-lg-12">
                  <ValidationProvider name="Số điện thoại" rules="required|min:10|max:11" v-slot="{ errors }">
                    <label>
                      Số điện thoại
                      <b class="text-danger">*</b>
                    </label>
                    <el-input
                      type="number"
                      :max="11"
                      v-model="form.phone"
                      placeholder="Nhập số điện thoại"
                      maxlength="11"
                    />
                    <span class="text-danger" v-if="errors.length">{{ errors[0] }}</span>
                  </ValidationProvider>
                </div>

                <div class="form-group col-lg-12">
                  <ValidationProvider name="Email" rules="email" v-slot="{ errors }">
                    <label>Email</label>
                    <el-input v-model="form.email" placeholder="Nhập email" />
                    <span class="text-danger" v-if="errors.length">{{ errors[0] }}</span>
                  </ValidationProvider>
                </div>

                <div class="form-group col-lg-12">
                  <label>
                    Ngày mua
                    <b class="text-danger">*</b>
                  </label>
                  <el-date-picker
                    v-model="form.orderDate"
                    format="dd/MM/yyyy"
                    placeholder="Chọn ngày mua hàng"
                    class="w-100"
                  />
                </div>

                <div class="form-group col-lg-12">
                  <label>
                    Ảnh hoá đơn
                    <b class="text-danger">*</b>
                  </label>
                  <el-upload
                    action="#"
                    accept="image/*"
                    :auto-upload="false"
                    :multiple="false"
                    :on-change="handleOrderImageChange"
                    list-type="picture"
                    :file-list="orderImageList"
                    :show-file-list="true"
                    :on-remove="handleProductImageRemove"
                    :limit="1"
                  >
                    <el-button :loading="isUploadBill" type="primary" icon="el-icon-upload">Tải lên ảnh</el-button>
                  </el-upload>
                </div>

                <div class="col-lg-12 form-group">
                  <label>
                    Ảnh sản phẩm
                    <b class="text-danger">*</b>
                  </label>
                  <br />
                  <input
                    type="file"
                    accept="image/*"
                    hidden
                    id="triggerProductImageChange"
                    @change="handleProductImageChange"
                  />
                  <el-button
                    :loading="isUploadProduct"
                    type="primary mb-4"
                    @click="handleUploadProduct"
                    icon="el-icon-upload"
                  >
                    Tải lên ảnh
                  </el-button>
                  <div class="card" v-for="(item, index) in form.products" :key="index">
                    <div class="card-body">
                      <el-button
                        type="danger"
                        circle
                        size="mini"
                        style="position: absolute; right: 10px; top: 10px"
                        icon="el-icon-close"
                        @click="handleRemoveProduct(index)"
                      />

                      <div class="card-title mb-0">
                        <h3>Sản phẩm {{ index + 1 }}</h3>
                        <p>{{ item.originalName }}</p>
                      </div>
                      <div class="row">
                        <div class="col-lg-6 col-md-6">
                          <ValidationProvider rules="required" name="Model" v-slot="{ errors }">
                            <label>
                              Model
                              <b class="text-danger">*</b>
                            </label>
                            <el-input type="text" v-model="form.products[index].model" />
                            <span style="font-size: 12px" class="text-danger" v-if="errors.length">
                              {{ errors[0] }}
                            </span>
                          </ValidationProvider>
                        </div>
                        <div class="col-lg-6 col-md-6">
                          <ValidationProvider rules="required" name="Serial" v-slot="{ errors }">
                            <label>
                              Serial
                              <b class="text-danger">*</b>
                            </label>
                            <el-input type="text" v-model="form.products[index].serial" />
                            <span style="font-size: 12px" class="text-danger" v-if="errors.length">
                              {{ errors[0] }}
                            </span>
                          </ValidationProvider>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="d-flex justify-content-end">
                <!--              <el-button>Huỷ</el-button>-->
                <el-button
                  :loading="isLoading"
                  type="primary"
                  :disabled="invalid || !form.products.length || !form.orderImageName || isLoading"
                  @click.prevent="handleSubmit"
                >
                  Lưu
                </el-button>
              </div>
            </div>
          </div>
        </ValidationObserver>
      </el-card>
    </div>
  </div>
</template>

<script>
  import { computed, defineComponent, ref, useContext, useRouter, useStore } from '@nuxtjs/composition-api';
  import { Card, Form, FormItem, Input, Button, DatePicker, Upload, Message } from 'element-ui';
  import { getApiErrorMessage } from '@/util/functions.js';
  import useImage from '@/composition/useImage.js';
  import { clientApi } from '@/util/client-api.js';
  import { useClaims } from '@/composition';

  export default defineComponent({
    name: 'ClaimCreate',
    layout: 'DashboardLayout',
    components: {
      [Form.name]: Form,
      [FormItem.name]: FormItem,
      [Input.name]: Input,
      [Card.name]: Card,
      [Button.name]: Button,
      [DatePicker.name]: DatePicker,
      [Upload.name]: Upload,
    },
    setup() {
      // const store = useStore();
      const { store } = useContext();
      const router = useRouter();
      const { $config } = useContext();
      const { $permissions } = useContext();
      if (!$permissions.canCreateClaim()) {
        router.push('/claims');
      }

      const { uploadImage } = useImage();
      const { createClaim } = useClaims();
      const isLoading = ref(false);
      const isUploadProduct = ref(false);
      const isUploadBill = ref(false);

      const form = ref({
        name: '',
        phone: null,
        email: '',
        orderDate: null,
        orderImageName: '',
        orderImageUrl: '',
        products: [],
      });

      const orderImageList = ref([]);
      const productImageList = ref([]);

      async function handleOrderImageChange(file) {
        try {
          isUploadBill.value = true;
          const url = $config.imageUrlUpload.replace('{fileName}', file.raw.name);
          const { data } = await clientApi.post(url, file.raw, {
            headers: {
              'Content-Type': file.type,
            },
          });
          form.value.orderImageName = data.filename;
          form.value.orderImageUrl = data.url;
          isUploadBill.value = false;
        } catch (e) {
          orderImageList.value = [];
          isUploadBill.value = false;
          Message({ type: 'error', message: getApiErrorMessage(e.response.data.error.code), duration: 5000 });
        }
      }

      function handleProductImageRemove() {
        form.value.orderImageName = '';
        form.value.orderImageUrl = '';
      }

      async function handleProductImageChange(event) {
        const file = event.target.files[0];
        document.getElementById('triggerProductImageChange').value = null;
        try {
          isUploadProduct.value = true;
          const { data } = await uploadImage(file);
          form.value.products.push({
            ...data,
            originalName: file.name,
            serial: '',
            model: '',
          });
          isUploadProduct.value = false;
        } catch (e) {
          Message({ type: 'error', message: getApiErrorMessage(e.response.data.error.code), duration: 5000 });
          isUploadProduct.value = false;
        }
      }

      function handleUploadProduct() {
        document.getElementById('triggerProductImageChange').click();
      }

      async function handleSubmit() {
        try {
          isLoading.value = true;
          let phone = form.value.phone.toString();
          if (!phone.startsWith('84')) {
            if (phone.startsWith('0')) {
              phone = phone.replace('0', '84');
            } else {
              phone = '84' + phone;
            }
          }

          const payload = {
            orderStoreId: null,
            orderId: '',
            orderDate: Math.floor(new Date(form.value.orderDate).getTime() / 1000),
            orderImage: form.value.orderImageName,
            note: '',
            ip: '',
            customerName: form.value.name.trim(),
            customerPhone: phone,
            customerEmail: form.value.email ? form.value.email.trim() : null,
            products: form.value.products.map((product) => ({
              serial: product.serial,
              model: product.model,
              image: product.filename,
            })),
          };

          await createClaim(payload);
          Message({
            message: 'Thêm mới đơn hàng thành cônng',
            type: 'success',
            duration: 5000,
          });
          router.push('/claims');
          isLoading.value = false;
        } catch (e) {
          isLoading.value = false;
          Message({ type: 'error', message: getApiErrorMessage(e.data.error.code), duration: 5000 });
        }
      }

      function handleRemoveProduct(index) {
        form.value.products.splice(index, 1);
      }

      return {
        form,
        orderImageList,
        productImageList,
        handleOrderImageChange,
        handleProductImageChange,
        handleUploadProduct,
        handleProductImageRemove,
        handleSubmit,
        handleRemoveProduct,
        isLoading,
        isUploadProduct,
        isUploadBill,
      };
    },
  });
</script>

<style scoped>
  span {
    font-size: 14px;
  }
</style>

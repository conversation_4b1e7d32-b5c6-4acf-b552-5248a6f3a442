<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12">
          <WhiteListSearch :params="params" />
        </div>
        <div class="col-lg-12">
          <WhiteList :params="params" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {defineComponent, reactive} from '@nuxtjs/composition-api';
import WhiteList from '@/components/pages/whitelist/WhiteList';
import WhiteListSearch from '@/components/pages/whitelist/WhiteListSearch';

export default defineComponent({
  layout: 'DashboardLayout',
  components: {WhiteList, WhiteListSearch},
  setup() {
    const params = reactive({
      code_check: null,
      status: 2,
      page: 1,
      limit: 10,
    });

    return {
      params};
  },
});
</script>

<style scoped></style>

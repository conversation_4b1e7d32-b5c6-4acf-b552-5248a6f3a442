<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12 py-3">
          <h1 class="mb-0">Chi tiết đơn hàng</h1>
        </div>
      </div>
      <div class="row" v-if="claim">
        <ClaimDetailGeneral :claim="claim" />
        <div class="col-lg-6" v-for="(item, index) in claim.claimDetail">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title mb-0">Thông tin chi tiết quà tặng {{ index + 1 }}</h3>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6 mb-4">
                  <div class="form-group">
                    <label class="form-label">Model</label>
                    <el-input disabled placeholder="Nhập model" :value="item.model"></el-input>
                  </div>
                  <div class="form-group">
                    <label class="form-label">Serial</label>
                    <el-input disabled placeholder="Nhập serial" :value="item.serial"></el-input>
                  </div>
                  <div class="form-group" v-if="claimDetail[item.id]">
                    <label class="form-label">Quà được nhận</label>
                    <el-select
                      :disabled="!!claim.approveTwo"
                      v-model="claimDetail[item.id].giftId"
                      @change="onGiftChange"
                      clearable
                    >
                      <el-option
                        v-for="gift in giftList"
                        :key="gift.id"
                        :value="gift.id"
                        :label="gift.title || gift.model"
                      ></el-option>
                    </el-select>
                  </div>
                </div>

                <div class="col-md-6 mb-4">
                  <label class="form-label">Ảnh serial/model</label>
                  <img class="w-100" :src="`${$config.baseImageUrl}${item.image}` || '/img/mockup.png'" alt="" />
                </div>

                <div class="col-md-12">
                  <el-alert
                    v-if="item.process === claimDetailProcess.PENDING.VALUE"
                    type="info"
                    :title="claimDetailProcess.PENDING.TEXT"
                    show-icon
                    :closable="false"
                  />
                  <el-alert
                    v-if="item.process === claimDetailProcess.CANNOT_GET_GIFT.VALUE"
                    type="error"
                    :title="claimDetailProcess.CANNOT_GET_GIFT.TEXT"
                    show-icon
                    :closable="false"
                  />
                  <el-alert
                    v-if="item.process === claimDetailProcess.CANNOT_SEND_SMS.VALUE"
                    type="error"
                    :title="claimDetailProcess.CANNOT_SEND_SMS.TEXT"
                    show-icon
                    :closable="false"
                  />
                  <el-alert
                    v-if="item.process === claimDetailProcess.DATA_CORRUPT.VALUE"
                    type="error"
                    :title="claimDetailProcess.DATA_CORRUPT.TEXT"
                    show-icon
                    :closable="false"
                  />
                  <el-alert
                    v-if="item.process === claimDetailProcess.SUCCESS.VALUE"
                    type="success"
                    :title="claimDetailProcess.SUCCESS.TEXT"
                    show-icon
                    :closable="false"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-lg-12">
          <div class="card">
            <div class="card-body">
              <ValidationObserver ref="approvalForm" v-slot="{ invalid }">
                <el-alert
                  center
                  type="error"
                  show-icon
                  closable
                  :title="errorMessage"
                  v-if="errorMessage"
                  class="mb-4"
                />
                <el-alert
                  center
                  type="success"
                  show-icon
                  closable
                  :title="successMessage"
                  v-if="successMessage"
                  class="mb-4"
                />

                <div class="row">
                  <div class="form-group col-lg-4">
                    <label class="form-label">Mô tả/Ghi chú</label>
                    <el-input type="textarea" rows="5" v-model="globalParams.note" placeholder="Ghi chú"></el-input>
                  </div>
                  <div class="form-group col-lg-4">
                    <div>
                      <label class="form-label">&nbsp</label>
                    </div>
                    <el-button type="primary" v-if="claim.process === claimProcess.PENDING.VALUE" @click="handleNote">
                      Bắt đầu xử lý
                    </el-button>
                    <el-button type="primary" v-if="claim.process !== claimProcess.PENDING.VALUE" @click="handleNote">
                      Lưu note
                    </el-button>
                  </div>
                </div>
                <!--                <div class="row">-->
                <!--                  <div class="form-group col-lg-4">-->
                <!--                    <label class="form-label">Ghi chú pending</label>-->
                <!--                    <el-input-->
                <!--                        :disabled="claim.process !== claimProcess.PENDING.VALUE"-->
                <!--                        v-model="pendingApproval.note"-->
                <!--                        placeholder="Ghi chú vì sao pending"-->
                <!--                    ></el-input>-->
                <!--                  </div>-->
                <!--                  <div class="form-group col-lg-4" v-if="claim.process !== claimProcess.PENDING.VALUE">-->
                <!--                    <label class="form-label">Người duyệt</label>-->
                <!--                    <el-input-->
                <!--                        :disabled="claim.process !== claimProcess.PENDING.VALUE"-->
                <!--                        v-model="pendingApproval.account"-->
                <!--                        placeholder="Ghi chú"-->
                <!--                    ></el-input>-->
                <!--                  </div>-->
                <!--                </div>-->

                <div class="row" v-if="claim.process !== claimProcess.PENDING.VALUE">
                  <div class="col-lg-4" v-if="!claim.approveOne">
                    <div class="form-group">
                      <label class="form-label">
                        Duyệt đợt 1
                        <b class="text-danger">*</b>
                      </label>
                      <el-select v-model="firstApproval.status" :disabled="!!claim.approveOne">
                        <el-option
                          v-for="item in approveStatusList"
                          :key="item.VALUE"
                          :value="item.VALUE"
                          :label="item.TEXT"
                        ></el-option>
                      </el-select>
                    </div>
                  </div>

                  <div class="form-group col-lg-4">
                    <label class="form-label">Tài khoản duyệt đợt 1</label>
                    <el-input
                      disabled
                      v-model="firstApproval.account"
                      placeholder="Nhập tài khoản duyệt đợt 1"
                    ></el-input>
                  </div>

                  <div
                    class="form-group col-lg-4"
                    v-if="
                      (firstApproval.status === approveStatus.DENY.VALUE && !claim.approveOne) || claim.approveOneNote
                    "
                  >
                    <ValidationProvider
                      name="Lý do từ chối"
                      :rules="!claim.approveOne ? 'required' : ''"
                      v-slot="{ errors }"
                    >
                      <label class="form-label">
                        Lý do từ chối
                        <b class="text-danger">*</b>
                      </label>
                      <el-input
                        :disabled="!!claim.approveOne"
                        :class="errors.length ? 'mb-4' : ''"
                        v-model="firstApproval.note"
                        placeholder="Nhập lý do từ chối"
                      ></el-input>
                      <validation-error :errors="errors" />
                    </ValidationProvider>
                  </div>
                </div>

                <div class="row" v-if="claim.approveOne">
                  <div class="col-lg-4" v-if="!claim.approveTwo">
                    <div class="form-group">
                      <label class="form-label">
                        Duyệt đợt 2
                        <b class="text-danger">*</b>
                      </label>
                      <el-select v-model="secondApproval.status" :disabled="!!claim.approveTwo">
                        <el-option
                          v-for="item in approveStatusList"
                          :key="item.VALUE"
                          :value="item.VALUE"
                          :label="item.TEXT"
                        ></el-option>
                      </el-select>
                    </div>
                  </div>

                  <div class="form-group col-lg-4">
                    <label class="form-label">Tài khoản duyệt đợt 2</label>
                    <el-input
                      disabled
                      v-model="secondApproval.account"
                      placeholder="Nhập tài khoản duyệt đợt 1"
                    ></el-input>
                  </div>

                  <div
                    class="form-group col-lg-4"
                    v-if="
                      (secondApproval.status === approveStatus.DENY.VALUE && !claim.approveTwo) || claim.approveTwoNote
                    "
                  >
                    <ValidationProvider name="Lý do từ chối" rules="required" v-slot="{ errors }">
                      <label class="form-label">
                        Lý do từ chối
                        <b class="text-danger">*</b>
                      </label>
                      <el-input
                        :disabled="!!claim.approveTwo"
                        :class="errors.length ? 'mb-4' : ''"
                        v-model="secondApproval.note"
                        placeholder="Nhập lý do từ chối"
                      ></el-input>
                      <validation-error :errors="errors" />
                    </ValidationProvider>
                  </div>
                </div>

                <div class="row">
                  <div class="col-lg-12 d-flex justify-content-end">
                    <el-button type="warning" v-if="isMustResendGift" @click="handleResendGift">Gửi lại quà</el-button>
                    <el-button
                      v-if="claim.process !== claimProcess.PENDING.VALUE"
                      type="primary"
                      @click="handleSubmit"
                      :disabled="!isGiftsSelected || invalid || (!!claim.approveOne && !!claim.approveTwo)"
                    >
                      Lưu
                    </el-button>
                  </div>
                </div>
              </ValidationObserver>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  defineComponent,
  reactive,
  useFetch,
  useStore,
  useRoute,
  computed,
  ref,
  useRouter, useContext,
} from '@nuxtjs/composition-api';
  import { Form, FormItem, Option, Select, Button, Alert, MessageBox, Message } from 'element-ui';
  import { CLAIM_PROCESS, APPROVE_STATUS, APPROVE_STATUS_LIST, CLAIM_DETAIL_PROCESS } from '@/util/constant.js';
  import { useGifts, useClaims, useApproval } from '../../composition';
  import ClaimDetailGeneral from '@/components/pages/claim/detail/ClaimDetailGeneral.vue';
  import LoadingPanel from '@/components/argon-core/LoadingPanel.vue';
  import LoadingContent from '@/components/LoadingContent.vue';

  export default defineComponent({
    name: 'ClaimDetail',
    layout: 'DashboardLayout',
    components: {
      LoadingContent,
      LoadingPanel,
      ClaimDetailGeneral,
      [Form.name]: Form,
      [FormItem.name]: FormItem,
      [Option.name]: Option,
      [Select.name]: Select,
      [Button.name]: Button,
      [Alert.name]: Alert,
    },
    setup() {
      // const store = useStore();
      const { store } = useContext();
      const route = useRoute();
      const router = useRouter();
      const claimId = route.value.params.id;

      const claim = ref(null);
      const errorMessage = ref('');
      const successMessage = ref('');
      const isGiftsSelected = ref(true);
      const isMustResendGift = ref(false);

      const { resentGift } = useClaims();
      const { fetchDetailClaim } = useClaims();
      const { fetchGiftList } = useGifts();

      const { doFirstApproval, doSecondApproval, doSaveNote } = useApproval();

      const giftList = computed(() => {
        return store.getters['gift/list'];
      });

      const user = computed(() => {
        return store.getters['loggedInUser'];
      });

      const claimDetail = ref({});
      const isFirstApproval = ref(true);

      const globalParams = reactive({
        note: '',
      });

      const pendingApproval = reactive({
        status: 1,
        note: '',
        account: '',
      });

      const firstApproval = reactive({
        status: 1,
        note: '',
        account: '',
      });

      const secondApproval = reactive({
        status: 1,
        note: '',
        account: '',
      });

      const initData = async () => {
        try {
          await fetchGiftList();
          const data = await fetchDetailClaim(claimId);

          if (data) {
            claim.value = data;

            const tempData = {};

            data.claimDetail.forEach((item) => {
              tempData[item.id] = {
                claimDetailId: item.id,
                giftId: Object.keys(item.gift).length ? item.gift.giftId : item.suggest.giftId,
              };
            });

            for (const item of data.claimDetail) {
              if (
                item.process === CLAIM_DETAIL_PROCESS.DATA_CORRUPT.VALUE ||
                item.process === CLAIM_DETAIL_PROCESS.CANNOT_SEND_SMS.VALUE ||
                item.process === CLAIM_DETAIL_PROCESS.CANNOT_GET_GIFT.VALUE
              ) {
                isMustResendGift.value = true;
                break;
              }
            }

            claimDetail.value = tempData;
            globalParams.note = data.note;

            onGiftChange(null);
            if (data.approveOne) {
              firstApproval.account = data.approveOne.email;
              firstApproval.note = data.approveOneNote || '';
              firstApproval.status =
                data.process === CLAIM_PROCESS.DENIED_1.VALUE || data.process === CLAIM_PROCESS.DENIED_2.VALUE
                  ? APPROVE_STATUS.DENY.VALUE
                  : APPROVE_STATUS.APPROVAL.VALUE;
            } else {
              firstApproval.account = user.value.email;
            }

            if (data && data.approvePending) {
              pendingApproval.account = data.approvePending.email;
              pendingApproval.note = data.approvePendingNote || '';
              pendingApproval.status =
                data.process === CLAIM_PROCESS.DENIED_1.VALUE || data.process === CLAIM_PROCESS.DENIED_2.VALUE
                  ? APPROVE_STATUS.DENY.VALUE
                  : APPROVE_STATUS.APPROVAL.VALUE;
            } else {
              pendingApproval.account = user.value.email;
            }

            if (data && data.approveTwo) {
              secondApproval.account = data.approveTwo.email;
              secondApproval.note = data.approveTwoNote || '';
              secondApproval.status =
                data.process === CLAIM_PROCESS.DENIED_1.VALUE || data.process === CLAIM_PROCESS.DENIED_2.VALUE
                  ? APPROVE_STATUS.DENY.VALUE
                  : APPROVE_STATUS.APPROVAL.VALUE;
            } else {
              secondApproval.account = user.value.email;
            }
          } else {
            router.push('/claims');
          }
        } catch (e) {
          Message({
            message: JSON.stringify(e.data.error.message),
            type: 'error',
            duration: 5 * 1000,
          });
          router.push('/claims');
        }
      };

      useFetch(async () => {
        await store.dispatch('setLoading', true);
        await initData();
        await store.dispatch('setLoading', false);
      });

      async function handleNote() {
        try {
          errorMessage.value = '';
          successMessage.value = '';

          await store.dispatch('setLoading', true);

          await doSaveNote(claim.value, globalParams.note);
          successMessage.value = 'Note saved';
          await initData();
        } catch (e) {
          if (e.data && e.data.message) {
            errorMessage.value = e.data.message;
          }
        } finally {
          await store.dispatch('setLoading', false);
        }
      }

      async function handleSubmit() {
        try {
          await store.dispatch('setLoading', true);
          errorMessage.value = '';
          successMessage.value = '';

          firstApproval.note = firstApproval.note.trim();
          secondApproval.note = secondApproval.note.trim();
          pendingApproval.note = pendingApproval.note.trim();

          if (!claim.value.approveOne) {
            if (firstApproval.status === APPROVE_STATUS.DENY.VALUE && !firstApproval.note) {
              return;
            }
            await doFirstApproval(claim.value, firstApproval, claimDetail.value, globalParams.note);
            successMessage.value =
              firstApproval.status === APPROVE_STATUS.APPROVAL.VALUE
                ? 'Duyệt lần 1 thành công'
                : 'Từ chối lần 1 thành công';
          } else {
            if (secondApproval.status === APPROVE_STATUS.DENY.VALUE && !secondApproval.note) {
              return;
            }
            await doSecondApproval(claim.value, secondApproval, claimDetail.value, globalParams.note);
            successMessage.value =
              secondApproval.status === APPROVE_STATUS.APPROVAL.VALUE
                ? 'Duyệt lần 2 thành công'
                : 'Từ chối lần 2 thành công';
          }
          await initData();
        } catch (e) {
          if (e.data && e.data.message) {
            errorMessage.value = e.data.message;
          }
        } finally {
          await store.dispatch('setLoading', false);
        }
      }

      async function handleResendGift() {
        await store.dispatch('setLoading', true);
        await resentGift(claim.value.id);
        await initData();
        await store.dispatch('setLoading', false);
      }

      const claimProcess = CLAIM_PROCESS;
      const approveStatus = APPROVE_STATUS;
      const approveStatusList = APPROVE_STATUS_LIST;

      function onGiftChange(_) {
        Object.keys(claimDetail.value).forEach((key) => {
          isGiftsSelected.value = !!claimDetail.value[key].giftId;
        });
      }

      function handleStartProcess() {
        console.log(1);
      }

      return {
        claimDetailProcess: CLAIM_DETAIL_PROCESS,
        approveStatus,
        approveStatusList,
        claimProcess,
        claim,
        giftList,
        firstApproval,
        secondApproval,
        pendingApproval,
        globalParams,
        handleSubmit,
        claimDetail,
        isFirstApproval,
        errorMessage,
        successMessage,
        isGiftsSelected,
        onGiftChange,
        isMustResendGift,
        handleResendGift,
        handleNote,
      };
    },
  });
</script>

<style>
  .form-label {
    font-size: 14px;
    font-weight: 600;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .el-input.is-disabled {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
  }

  .el-select .el-input .el-input__inner:disabled {
    background-color: #f5f7fa;
    opacity: 1;
  }
</style>

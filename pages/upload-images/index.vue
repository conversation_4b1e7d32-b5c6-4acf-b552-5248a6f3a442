<template>
  <div>
    <div class="container-fluid mt-4">
      <el-card shadow="always" class="box-card">
        <div slot="header" class="clearfix">
          <span>Upload ảnh sản phẩm</span>
        </div>
        <ValidationObserver v-slot="{ invalid }">
          <div class="row justify-content-center">
            <div class="col-lg-8">
              <div class="row">
                <div class="col-lg-12 form-group">
                  <label>
                    Ảnh sản phẩm
                    <b class="text-danger">*</b>
                  </label>
                  <br />
                  <input type="file" accept="image/*" hidden id="triggerProductImageChange"
                    @change="handleProductImageChange" />
                  <el-button :loading="isUploadProduct" type="primary mb-4" @click="handleUploadProduct"
                    icon="el-icon-upload">
                    Tải lên ảnh
                  </el-button>
                  <div class="card" v-for="(item, index) in form.products" :key="index">
                    <div class="card-body">
                      <el-button type="danger" circle size="mini" style="position: absolute; right: 10px; top: 10px"
                        icon="el-icon-close" @click="handleRemoveProduct(index)" />

                      <div class="card-title mb-0">
                        <h3>Ảnh {{ index + 1 }}</h3>
                        <p>{{ item.filename }}</p>
                      </div>
                      <div>
                        <img :src="item.url" width="50%" height="50%">
                      </div>          
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ValidationObserver>
      </el-card>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, useContext, useRouter } from '@nuxtjs/composition-api';
import { Card, Form, FormItem, Input, Button, DatePicker, Upload, Message } from 'element-ui';
import { getApiErrorMessage } from '@/util/functions.js';
import useImage from '@/composition/useImage.js';


export default defineComponent({
  name: 'UploadImages',
  layout: 'DashboardLayout',
  components: {
    [Form.name]: Form,
    [FormItem.name]: FormItem,
    [Input.name]: Input,
    [Card.name]: Card,
    [Button.name]: Button,
    [DatePicker.name]: DatePicker,
    [Upload.name]: Upload,
  },
  setup() {
    const { store , $permissions} = useContext();
    const router = useRouter();

    if (!$permissions.canCreateClaim()) {
      router.push('/claims');
    }

    const { uploadImage } = useImage();
    const isLoading = ref(false);
    const isUploadProduct = ref(false);
    const isUploadBill = ref(false);

    const form = ref({
      products: [],
    });

    async function handleProductImageChange(event) {
      const file = event.target.files[0];
      document.getElementById('triggerProductImageChange').value = null;
      try {
        isUploadProduct.value = true;
        const { data } = await uploadImage(file);
        console.log(data);
        form.value.products.push({
          ...data,
          originalName: file.name,
          serial: '',
          model: '',
        });
        isUploadProduct.value = false;
      } catch (e) {
        Message({ type: 'error', message: getApiErrorMessage(e.response.data.error.code), duration: 5000 });
        isUploadProduct.value = false;
      }
    }

    function handleUploadProduct() {
      document.getElementById('triggerProductImageChange').click();
    }

    function handleRemoveProduct(index) {
      form.value.products.splice(index, 1);
    }

    return {
      form,
      handleProductImageChange,
      handleUploadProduct,
      handleRemoveProduct,
      isLoading,
      isUploadProduct,
      isUploadBill,
    };
  },
});
</script>

<style scoped>
span {
  font-size: 14px;
}
</style>

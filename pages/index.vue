<template>
  <div>
    <base-header class="pb-6">
      <div class="row align-items-center py-4">
        <div class="col-lg-12 d-flex flex-column align-items-center justify-content-center" style="min-height: 200px;">
          <div v-if="$permissions.isUserLoaded()" class="text-center">
            <h2 class="text-xl font-bold mb-2">Chào mừng, {{ userName }}!</h2>
            <p class="mb-1">Email: <span class="font-mono">{{ userEmail }}</span></p>
          </div>
          <div v-else class="text-center">
            <span>Đang tải thông tin người dùng...</span>
          </div>
        </div>
      </div>
    </base-header>
    <div class="container mt-4">
      <div v-if="$permissions.isUserLoaded()">
        <h3 class="text-lg font-semibold mb-2"><PERSON><PERSON><PERSON><PERSON> truy cập của bạn</h3>
        <div class="d-flex flex-column gap-2">
          <div v-for="(permissions, resource) in groupedPermissions" :key="resource" class="mb-2">
            <el-tag
              v-for="permission in permissions"
              :key="permission.name"
              type="success"
              class="mb-2 mr-2"
              effect="plain"
              disable-transitions
            >
              <span class="font-weight-bold text-blue">
                {{ permission.display_name || permission.name }}
              </span>
            </el-tag>
          </div>
        </div>
      </div>
    </div>
    
  </div>
</template>
<script>
  // Charts

  import RouteBreadCrumb from '@/components/argon-core/Breadcrumb/RouteBreadcrumb';
  import StatsCard from '@/components/argon-core/Cards/StatsCard';
  import SocialTrafficTable from '@/components/pages/dashboard/SocialTrafficTable.vue';
  import PageVisitsTable from '@/components/pages/dashboard/PageVisitsTable.vue';
  import { Tag } from 'element-ui'

  export default {
    layout: 'DashboardLayout',
    components: {
      RouteBreadCrumb,
      StatsCard,
      PageVisitsTable,
      SocialTrafficTable,
      [Tag.name]: Tag
    },
    computed: {
      userData () {
        return this.$permissions.getUserData() || {}
      },
      userName () {
        return this.userData.name || ''
      },
      userEmail () {
        return this.userData.email || ''
      },
      userPermissions () {
        return this.$permissions.getUserPermissions() || []
      },
      groupedPermissions () {
        // Group permissions by resource
        const groups = {}
        this.userPermissions.forEach(p => {
          const resource = p.resource || 'Khác'
          if (!groups[resource]) groups[resource] = []
          groups[resource].push(p)
        })
        return groups
      }
    },
    methods: {
      goToClaims () {
        this.$router.push('/claims')
      }
    }
  };
</script>
<style></style>

<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12 py-3">
        </div>
      </div>
      <div class="row">
        <div class="col-lg-12">
          <el-alert
              center
              type="error"
              show-icon
              closable
              :title="errorMessage"
              v-if="errorMessage"
              class="mb-4"
          />
          <el-alert
              center
              type="success"
              show-icon
              closable
              :title="successMessage"
              v-if="successMessage"
              class="mb-4"
          />
          <div class="card">
            <div class="card-header">
              <h3 class="card-title mb-0">Tạo campaign</h3>
            </div>
            <div class="card-body">
              <CampaignForm :entity="entity" :successMessage="successMessage" :errorMessage="errorMessage"/>
            </div>
          </div>
        </div>


      </div>
    </div>
  </div>
</template>

<script>
import {
  defineComponent,
  reactive,
  useFetch,
  useRoute,
  ref,
  useRouter, useContext, computed,
} from '@nuxtjs/composition-api';
import {Message} from 'element-ui';
import {useCampaign} from '~/composition';
import CampaignForm from '~/components/pages/campaign/CampaignForm';
import LoadingPanel from '~/components/argon-core/LoadingPanel';
import LoadingContent from '~/components/LoadingContent';
import {clone} from "lodash";

export default defineComponent({
  name: 'CreateCampaign',
  layout: 'DashboardLayout',
  components: {
    LoadingContent,
    LoadingPanel,
    CampaignForm,
  },
  setup(props) {
    const date = new Date();
    const end_time = new Date(date.setMonth(date.getMonth()+3));
    const entity = reactive({
      app_id: 0,
      id: 0,
      title: '',
      start_time: new Date(),
      end_time: end_time,
      end_time_for_admin: end_time,
      status: 2,
      has_store: '1',
      passport_require: '1',
      physical_gift: '2',
      k_plus: '1',
      show_lg_card: '2',
      show_urbox_card: '1',
      is_combo: '1',
      error_message: '',
    });

    return {
      successMessage: null,
      errorMessage: null,
      entity,
    };
  },
});
</script>


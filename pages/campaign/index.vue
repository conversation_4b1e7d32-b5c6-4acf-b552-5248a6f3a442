<template>
  <div class="container-fluid">
    <div class="row">
      <div class="col-lg-12">
        <!--          <GiftSearchForm :params="params" />-->
      </div>
      <div class="col-lg-12 mt-5">
        <CampaignList :params="params" />
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive } from '@nuxtjs/composition-api';
import CampaignList from '~/components/pages/campaign/CampaignList';

export default defineComponent({
  layout: 'DashboardLayout',
  components: { CampaignList },
  setup() {
    const params = reactive({
      all: true,
      status: null,
      page: 1,
      limit: 10,
      campaign_id: 33
    });

    return { params };
  },
});
</script>

<style scoped></style>

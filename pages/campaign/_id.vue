<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12 py-3">
        </div>
      </div>
      <div class="row" v-if="campaign">
        <div class="col-lg-12">
          <el-alert center type="error" show-icon closable :title="errorMessage" v-if="errorMessage" class="mb-4" />
          <el-alert center type="success" show-icon closable :title="successMessage" v-if="successMessage"
            class="mb-4" />
          <div class="card">
            <div class="card-header">
              <h3 class="card-title mb-0">Cập nhật campaign</h3>
            </div>
            <div class="card-body">
              <CampaignForm :entity="entity" :successMessage="successMessage" :errorMessage="errorMessage" />
            </div>
          </div>
        </div>


      </div>
    </div>
  </div>
</template>

<script>
import {
  defineComponent,
  reactive,
  useFetch,
  useRoute,
  ref,
  useRouter, useContext, computed,
} from '@nuxtjs/composition-api';
import { Message } from 'element-ui';
import { useCampaign } from '~/composition';
import CampaignForm from '~/components/pages/campaign/CampaignForm';
import LoadingPanel from '~/components/argon-core/LoadingPanel';
import LoadingContent from '~/components/LoadingContent';

export default defineComponent({
  name: 'CampaignDetail',
  layout: 'DashboardLayout',
  components: {
    LoadingContent,
    LoadingPanel,
    CampaignForm,
  },
  setup(props) {
    const { store } = useContext();
    const route = useRoute();
    const router = useRouter();
    const { fetchCampaignDetail, campaign } = useCampaign();


    const entity = reactive({
      app_id: 0,
      id: 0,
      title: '',
      start_time: 0,
      end_time: 0,
      end_time_for_admin: 0,
      status: 2,
      email: '',
      description: '',
      name: '',
      thumbnail_image: '',
      gift_image: '',
      kv_mobile: '',
      kv_desktop: '',
      invoice_image: '',
      gift_card_image: '',
      guide_banner_image: '',
      thumbnail_image_img: '',
      gift_image_img: '',
      kv_mobile_img: '',
      kv_desktop_img: '',
      invoice_image_img: '',
      gift_card_image_img: '',
      guide_banner_img: '',
      is_combo: '1',
      show_urbox_card: '1',
      show_lg_card: '2',
      k_plus: '1',
      physical_gift: '2',
      passport_require: '2',
      has_store: '1',
      tnc: '',
      error_message: '',
      second_phone: "1",
    });
    const initData = async () => {
      try {
        function parseDate(dateStr, defaultDate) {
          if (dateStr === "" || dateStr === undefined) return defaultDate;
          const [day, month, year] = dateStr.split('/').map(Number);
          return new Date(year, month - 1, day); // Tháng bắt đầu từ 0 (0 = Jan, 1 = Feb, ...)
        }
        await fetchCampaignDetail(route.value?.params?.id || 0);
        if (campaign) {
          const item = campaign.value;
          Object.assign(entity, item);
          Object.assign(entity, item.meta_data);
          entity.start_time = new Date(item.start_time * 1000);
          entity.end_time = new Date(item.end_time * 1000);
          entity.buy_start_date = parseDate(item.meta_data.buy_start_date, entity.start_time);
          entity.buy_end_date = parseDate(item.meta_data.buy_end_date, entity.end_time);
          entity.end_time_for_admin = new Date(item.end_time_for_admin * 1000);
        } else {
          return router.push('/campaign');
        }
      } catch (e) {
        console.log(e);
        Message({
          message: JSON.stringify(e.data?.error?.message),
          type: 'error',
          duration: 5 * 1000,
        });
        router.push('/campaign');
      }
    };

    useFetch(async () => {
      store.dispatch('setLoading', true);
      await initData().then(() => {
        store.dispatch('setLoading', false);
      });
    });


    return {
      successMessage: null,
      errorMessage: null,
      entity,
      campaign,
    };
  },
});
</script>

<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12">
          <ClaimSearchForm :params="params"></ClaimSearchForm>
        </div>
        <div class="col-lg-12">
          <claim-list :params="params"></claim-list>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { defineComponent, reactive } from '@nuxtjs/composition-api';
  import ClaimList from '@/components/pages/claim-temp/ClaimList.vue';
  import ClaimSearchForm from '@/components/pages/claim-temp/ClaimSearchForm.vue';

  export default defineComponent({
    layout: 'DashboardLayout',
    components: { ClaimList, ClaimSearchForm },
    middleware: 'permission',
    setup() {
      const params = reactive({
        name: '',
        phone: null,
        email: '',
        model: '',
        serial: '',
        process: null,
        orderDate: null,
        orderStoreId: null,
        page: 1,
        limit: 20,
        matched: null,
      });

      return { params };
    },
  });
</script>

<style scoped></style>

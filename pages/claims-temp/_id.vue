<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12 py-3">
          <h1 class="mb-0">Chi tiết đơn hàng</h1>
        </div>
      </div>
      <div class="row" v-if="claim">
        <ClaimDetailGeneral :claim="claim" :user="user" :initData="initData" />
        <div class="col-lg-6" v-bind:key="item.id" v-for="item in claimDetails">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title mb-0">
                Thông tin chi tiết quà tặng: {{ item?.gift?.title }}
                <span v-if="item.process === 2" class="badge text-wrap badge-success mb-1 lh-120">
                  Hợp lệ
                </span>
              </h3>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6 mb-4">
                  <div class="form-group">
                    <label class="form-label">Model</label>
                    <el-input placeholder="Model" class="el-form-item mb-0" v-model="item.model"></el-input>
                  </div>
                  <div class="form-group">
                    <label class="form-label">Serial</label>
                    <el-input placeholder="Nhập serial" v-model="item.serial"
                              class="el-form-item mb-0"></el-input>
                  </div>
                </div>
                <div class="col-md-6 mb-4">
                  <label class="form-label">Ảnh serial/model</label>
                  <img class="w-100" :src="`${$config.baseImageUrl}${item.image}` || '/img/mockup.png'" alt="" />
                </div>
                <div class="col-md-6 mb-4">
                </div>
                <div class="col-md-6 mb-4 d-flex justify-content-end">
                  <el-button v-if="claim.process !== claimProcess.SUCCESS.VALUE" type="success" @click="updateItem(item.id)">
                    Cập nhật
                  </el-button>
                  <el-button v-if="claim.process !== claimProcess.SUCCESS.VALUE" type="danger" @click="deleteItem(item.id)">
                    Xóa
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12" v-if="claim.needCheckCombo">
          <div class="card">
            <div class="card-body">
              <el-alert v-if="claim.isCouple" type="success" title="Model trùng nhau" show-icon :closable="false" />
              <el-alert v-if="!claim.isCouple" type="error" title="Model không trùng nhau" show-icon
                        :closable="false" />
            </div>
          </div>
        </div>
        <div class="col-lg-12" v-if="globalParams.hasDeliveryAddress">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title mb-0">Địa chỉ nhận quà vật lý</h3>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="form-group col-lg-4">
                  <label class="form-label">Tên người nhận</label>
                  <el-input type="text" v-model="globalParams.delivery_name" readonly
                            placeholder="Tên người nhận"></el-input>
                </div>
                <div class="form-group col-lg-4">
                  <label class="form-label">Số điện thoại</label>
                  <el-input type="text" v-model="globalParams.delivery_phone" readonly
                            placeholder="Số điện thoại"></el-input>
                </div>
                <div class="form-group col-lg-4">
                  <label class="form-label">Địa chỉ</label>
                  <el-input type="text" v-model="globalParams.delivery_address" readonly
                            placeholder="Địa chỉ"></el-input>
                </div>
              </div>
              <div class="row">
                <div class="form-group col-lg-4">
                  <label class="form-label">Tỉnh/Thành phố</label>
                  <el-select readonly v-model="globalParams.delivery_province_id" clearable>
                    <el-option v-for="province in globalParams.provinceList" :key="province.id" :value="province.id"
                               :label="province.title"></el-option>
                  </el-select>
                </div>
                <div class="form-group col-lg-4">
                  <label class="form-label">Quận/Huyện</label>
                  <el-select readonly="readonly" v-model="globalParams.delivery_district_id" clearable>
                    <el-option v-for="district in globalParams.districtList" :key="district.id" :value="district.id"
                               :label="district.title"></el-option>
                  </el-select>
                </div>
                <div class="form-group col-lg-4">
                  <label class="form-label">Phường/Xã</label>
                  <el-select readonly v-model="globalParams.delivery_ward_id" clearable>
                    <el-option v-for="ward in globalParams.wardList" :key="ward.id" :value="ward.id"
                               :label="ward.title"></el-option>
                  </el-select>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12">
          <div class="card">
            <div class="card-body">
              <ValidationObserver ref="approvalForm" v-slot="{ invalid }">
                <el-alert center type="error" show-icon closable :title="errorMessage" v-if="errorMessage"
                          class="mb-4" />
                <el-alert center type="success" show-icon closable :title="successMessage" v-if="successMessage"
                          class="mb-4" />
                <div class="row">
                  <div class="col-lg-12 d-flex justify-content-end">
                    <el-button type="warning" v-if="claim.process !== claimProcess.SUCCESS.VALUE"
                               @click="accessClaim(claim.id)">
                      Chấp nhận đơn
                    </el-button>
                  </div>
                </div>
              </ValidationObserver>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import {
    computed,
    defineComponent,
    reactive,
    ref,
    useContext,
    useFetch,
    useRoute,
    useRouter,
  } from '@nuxtjs/composition-api';
  import {
    Alert,
    Button,
    Form,
    FormItem,
    Message,
    Option,
    Select,
    Table,
    TableColumn,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    Icon,
  } from 'element-ui';

  import { CLAIM_TEMP_PROCESS } from '@/util/constant.js';
  import { useApproval, useClaimsTemp, useDeliveryAddress, useGifts } from '@/composition';
  import ClaimDetailGeneral from '@/components/pages/claim-temp/detail/ClaimDetailGeneral';
  import LoadingPanel from '@/components/argon-core/LoadingPanel';
  import LoadingContent from '@/components/LoadingContent';

  export default defineComponent({
    name: 'ClaimDetail',
    layout: 'DashboardLayout',
    components: {
      LoadingContent,
      LoadingPanel,
      ClaimDetailGeneral,
      [Form.name]: Form,
      [FormItem.name]: FormItem,
      [Option.name]: Option,
      [Select.name]: Select,
      [Button.name]: Button,
      [Alert.name]: Alert,
      [Table.name]: Table,
      [TableColumn.name]: TableColumn,
      [Dropdown.name]: Dropdown,
      [DropdownItem.name]: DropdownItem,
      [DropdownMenu.name]: DropdownMenu,
      [Icon.name]: Icon,
    },
    setup() {
      // const store = useStore();
      const { store } = useContext();
      const route = useRoute();
      const router = useRouter();
      const claimId = route.value.params.id;

      const loadingState = ref(false);
      const claim = ref(null);
      const errorMessage = ref('');
      const successMessage = ref('');

      const { fetchDetailClaim } = useClaimsTemp();
      const { fetchDeliveryAddress, fetchProvince, fetchDistrict, fetchWard } =
        useDeliveryAddress();
      const { fetchGiftList } = useGifts();
      const { fetchNoteLog } = useApproval();
      const { id: campaignId } = store.getters['campaignStore/currentCampaign'];

      const user = computed(() => {
        return store.getters['loggedInUser'];
      });

      const claimLogs = ref({});

      const globalParams = reactive({
        note: '',
        delivery_name: '',
        delivery_phone: '',
        delivery_address: '',
        delivery_province_id: '',
        delivery_district_id: '',
        delivery_ward_id: '',
        provinceList: {},
        districtList: {},
        wardList: {},
        hasDeliveryAddress: false,
      });

      const claimDetails = reactive([]);
      const initData = async () => {
        claimDetails.length = 0;
        try {
          await fetchGiftList();
          const data = await fetchDetailClaim(claimId);
          const deliveryAddress = await fetchDeliveryAddress(claimId);
          const claimLogDatas = await fetchNoteLog(claimId);
          if (claimLogs) {
            claimLogs.value = claimLogDatas;
          }
          console.log(claimLogDatas);
          if (deliveryAddress && deliveryAddress.provinceId) {
            globalParams.hasDeliveryAddress = true;
            const provinces = await fetchProvince();
            globalParams.delivery_name = deliveryAddress.name;
            globalParams.delivery_phone = deliveryAddress.phone;
            globalParams.delivery_address = deliveryAddress.address;
            globalParams.provinceList = provinces;
            globalParams.delivery_province_id = deliveryAddress.provinceId;
            globalParams.delivery_district_id = deliveryAddress.districtId;
            globalParams.delivery_ward_id = deliveryAddress.wardId;

            const districts = await fetchDistrict(deliveryAddress.provinceId);
            globalParams.districtList = districts;
            const wards = await fetchWard(deliveryAddress.provinceId, deliveryAddress.districtId);
            globalParams.wardList = wards;
          }
          if (data) {
            claim.value = data;
            globalParams.note = data.note;
            data?.claimDetail?.forEach(item => {
              claimDetails.push({ ...item });
            });

          } else {
            router.push('/claims-temp');
          }
        } catch (e) {
          Message({
            message: JSON.stringify(e.data.error.message),
            type: 'error',
            duration: 5 * 1000,
          });
          router.push('/claims-temp');
        }
      };

      useFetch(async () => {
        await store.dispatch('setLoading', true);
        await initData();
        await store.dispatch('setLoading', false);
      });

      async function handleSubmit() {
        try {
          await store.dispatch('setLoading', true);
          await initData();
        } catch (e) {
          if (e.data && e.data.message) {
            errorMessage.value = e.data.message;
          }
        } finally {
          await store.dispatch('setLoading', false);
        }
      }

      async function deleteItem(itemId) {
        try {
          await store.dispatch('setLoading', true);
          const response = await this.$axios.post('temp/claim-detail/update', {
            claim_detail_id: itemId,
            status: -2,
          });
          if (response.success && response.code === 200 && response.data) {
            Message({
              message: 'Xóa thành công',
              type: 'success',
              duration: 5 * 1000,
            });
          }
          await initData();
        } catch (e) {
          Message({
            message: e.data.message || 'Có lỗi khi xóa',
            type: 'error',
            duration: 5 * 1000,
          });
        } finally {
          await store.dispatch('setLoading', false);
        }
      }

      async function updateItem(itemId) {

        const claimDetail = claimDetails.find(item => item.id === itemId);
        if (!claimDetail) return;
        try {
          await store.dispatch('setLoading', true);
          const response = await this.$axios.post('temp/claim-detail/update', {
            claim_detail_id: itemId,
            serial: claimDetail.serial,
            model: claimDetail.model,
            status: claimDetail.status,
            updated_by: user.value.id,
            updated_email: user.value.email,
          });
          if (response.success && response.code === 200 && response.data) {
            Message({
              message: 'Cập nhật thành công',
              type: 'success',
              duration: 5 * 1000,
            });
          }
          await initData();
        } catch (e) {
          Message({
            message: e.data.message || 'Có lỗi khi xóa',
            type: 'error',
            duration: 5 * 1000,
          });
        } finally {
          await store.dispatch('setLoading', false);
        }
      }

      async function accessClaim(itemId) {
        try {
          await store.dispatch('setLoading', true);
          const response = await this.$axios.post(`/save-claim/${itemId}?v2=1`, {
            campaign_id: campaignId,
          });
          if (response.success && response.code === 200 && response.data) {
            Message({
              message: 'Chấp nhận đơn thành công',
              type: 'success',
              duration: 5 * 1000,
            });
          }
          // await initData();
        } catch (e) {
          Message({
            message: e.data.message || 'Có lỗi khi chấp nhận đơn',
            type: 'error',
            duration: 5 * 1000,
          });
        } finally {
          await store.dispatch('setLoading', false);
        }
      }

      return {
        claimProcess: CLAIM_TEMP_PROCESS,
        claim,
        globalParams,
        handleSubmit,
        deleteItem,
        updateItem,
        errorMessage,
        successMessage,
        user,
        loadingState,
        initData,
        accessClaim,
        claimDetails,
      };
    },
  });
</script>

<style>
  .form-label {
    font-size: 14px;
    font-weight: 600;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .el-input.is-disabled {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
  }

  .el-input.is-unset .el-input__inner {
    border-color: #e4e7ed !important;
  }

  .el-select .el-input .el-input__inner:disabled {
    background-color: #f5f7fa;
    opacity: 1;
  }

  li.el-select-dropdown__item {
    white-space: normal;
    word-wrap: break-word;
    height: fit-content;
  }

  li.el-select-dropdown__item:nth-child(even) {
    background-color: rgb(245, 245, 245);
  }

  li.el-select-dropdown__item:nth-child(even):hover {
    background-color: rgb(248, 248, 248);
  }

  ul.el-select-dropdown__list {
    max-width: 60vw;
  }

  .el-table .cell {
    justify-content: center;
  }

</style>

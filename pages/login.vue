<template>
  <div>
    <div class="cl"></div>
    <div class="header header-login py-7 py-lg-8 pt-lg-9">
      <div class="container">
        <notifications></notifications>
        <div class="header-body text-center mb-7">
          <div class="text-center" style="margin-bottom: 5px"></div>
        </div>
      </div>
    </div>
    <!-- Page content -->
    <div class="container login-container mt--12 pb-5">
      <div class="row justify-content-center">
        <div class="col-lg-5 col-md-7">
          <div class="card border-0 mb-0">
            <div class="card-body px-lg-5 py-lg-5">
              <div class="text-center text-muted mb-4">
                <img style="width: 50%" src="/logo_urbox.png" alt="" />
              </div>

              <ValidationObserver ref="loginForm" v-slot="{ invalid }">
                <form class="needs-validation" @submit.prevent="onSubmit">
                  <ValidationProvider name="Email" rules="required|email" v-slot="{ errors }">
                    <base-input
                      alternative
                      class="mb-3"
                      name="Email"
                      prepend-icon="ni ni-email-83"
                      placeholder="Nhập Email"
                      v-model="form.email"
                    ></base-input>
                    <validation-error :errors="errors" />
                  </ValidationProvider>

                  <ValidationProvider name="Mật khẩu" rules="required" v-slot="{ errors }">
                    <base-input
                      alternative
                      class="mb-3"
                      name="Password"
                      prepend-icon="ni ni-lock-circle-open"
                      type="password"
                      placeholder="Nhập mật khẩu"
                      v-model="form.password"
                    ></base-input>
                    <validation-error :errors="errors" />
                  </ValidationProvider>
                  <div class="row mt-1 align-items-center">
                    <div class="col-12">
                      <base-button
                        :disabled="invalid"
                        :loading="isLoading"
                        type="primary"
                        native-type="submit"
                        class="w-100"
                      >
                        Đăng nhập
                      </base-button>
                    </div>
                  </div>
                </form>
              </ValidationObserver>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import ValidationError from '~/components/ValidationError.vue';
  import formMixin from '@/mixins/form-mixin';
  import Notification from '@/components/argon-core/NotificationPlugin/Notification.vue';

  export default {
    layout: 'AuthLayout',
    mixins: [formMixin],
    components: { Notification, ValidationError },
    data() {
      return {
        isLoading: false,
        error: '',
        form: {
          email: '',
          password: '',
        },
      };
    },
    methods: {
      async onSubmit() {
        try {
          this.error = '';
          const isValid = await this.$refs.loginForm.validate();
          if (!isValid) {
            return;
          }
          this.isLoading = true;

          // Đăng nhập
          console.log('🔐 Starting login process...');
          await this.$auth.loginWith('local', { data: this.form });
          console.log('✅ Login successful, auth state:', this.$auth.loggedIn);
          
          // Đợi một chút để auth state được cập nhật hoàn toàn
          await new Promise(resolve => setTimeout(resolve, 100));
          
          this.$axios.defaults.headers.common.Authorization = this.$auth.getToken('local');
          console.log('🔑 Authorization header set:', this.$axios.defaults.headers.common.Authorization);
          
          // Load user data ngay sau khi login thành công
          try {
            console.log('🔧 Loading user data after login...');
            console.log('Auth state before loading:', { 
              loggedIn: this.$auth.loggedIn, 
              token: this.$auth.getToken('local') 
            });
            const userData = await this.$store.dispatch('loadUserData', {
              $axios: this.$axios,
              $auth: this.$auth
            });
            console.log('✅ User data loaded after login:', userData);
          } catch (userDataError) {
            console.error('❌ Failed to load user data after login:', userDataError);
            // Không throw error để không làm gián đoạn login flow
          }
          
          // Redirect đến trang chính
          console.log('🔄 Redirecting to /claims...');
          await this.$router.push('/claims');

          this.isLoading = false;
        } catch (error) {
          console.error('❌ Login error:', error);
          if (error.data) {
            await this.$notify({
              type: 'danger',
              message: 'Tài khoản hoặc mật khẩu không chính xác',
            });
          }

          this.isLoading = false;
        }
      },
    },
  };
</script>

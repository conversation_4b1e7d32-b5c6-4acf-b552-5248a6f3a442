<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-12 py-3">
        </div>
      </div>
      <div class="row" v-if="giftDetail">
        <div class="col-lg-12">
          <el-alert
              center
              type="error"
              show-icon
              closable
              :title="errorMessage"
              v-if="errorMessage"
              class="mb-4"
          />
          <el-alert
              center
              type="success"
              show-icon
              closable
              :title="successMessage"
              v-if="successMessage"
              class="mb-4"
          />
          <div class="card">
            <div class="card-header">
              <h3 class="card-title mb-0">Cập nhật quà tặng</h3>
            </div>
            <div class="card-body">
              <GiftForm :entity="entity" :successMessage="successMessage" :errorMessage="errorMessage"/>
            </div>
          </div>
        </div>


      </div>
    </div>
  </div>
</template>

<script>
import {
  defineComponent,
  reactive,
  useFetch,
  useRoute,
  ref,
  useRouter, useContext, computed,
} from '@nuxtjs/composition-api';
import {Message} from 'element-ui';
import {useGifts} from '~/composition';
import GiftForm from '~/components/pages/gift/GiftForm';
import LoadingPanel from '~/components/argon-core/LoadingPanel';
import LoadingContent from '~/components/LoadingContent';

export default defineComponent({
  name: 'GiftDetail',
  layout: 'DashboardLayout',
  components: {
    LoadingContent,
    LoadingPanel,
    GiftForm,
  },
  setup(props) {
    const {store} = useContext();
    const route = useRoute();
    const router = useRouter();
    const errorMessage = ref('');
    const {fetchGiftDetail, giftDetail} = useGifts();

    const currentCampaign = computed(() => {
      return store.getters['campaignStore/currentCampaign'];
    });


    const entity = reactive({
      id: '',
      category_id: 1,
      gift_detail_id: 0,
      model: '',
      valuex: 0,
      title: '',
      status: 2,
      campaign_id: currentCampaign.id,
      related_model: ''
    });

    const initData = async () => {
      try {
        await fetchGiftDetail(route.value?.params?.id || 0);
        if (giftDetail) {
          const gift = giftDetail.value
          entity.id = gift.id;
          entity.category_id = gift.category_id;
          entity.gift_detail_id = gift.gift_detail_id;
          entity.model = gift.model;
          entity.valuex = gift.valuex;
          entity.title = gift.title;
          entity.status = gift.status;
          entity.campaign_id = gift.campaign_id;
          entity.related_model = gift.related_model;

        } else {
          return router.push('/gifts');
        }
      } catch (e) {
        Message({
          message: JSON.stringify(e.data.error.message),
          type: 'error',
          duration: 5 * 1000,
        });
        router.push('/gifts');
      }
    };

    useFetch(async () => {
      store.dispatch('setLoading', true);
      await initData().then(() => {
        store.dispatch('setLoading', false)
      });
    });


    return {
      successMessage: null,
      errorMessage: null,
      entity,
      giftDetail
    };
  },
});
</script>


FROM node:16-slim as build-stage

WORKDIR /app
COPY . .

ENV BASE_URL="https://claimapi.urbox.dev/v1"
ENV HOST="0.0.0.0"
ENV IMAGE_URL_UPLOAD="https://claimapi.urbox.dev/v1/files/{fileName}/storage"
ENV BASE_IMAGE_URL="https://file-cdn.urbox.dev/"

RUN npm install && yarn build

# production stage
FROM nginx:1.17-alpine as production-stage
COPY ./nginx.default.conf /etc/nginx/conf.d/default.conf
COPY --from=build-stage /app/dist /usr/share/nginx/html
CMD ["nginx", "-g", "daemon off;"]

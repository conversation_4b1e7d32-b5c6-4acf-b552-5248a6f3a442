const MUTATION_TYPE = {
  SET_WHITELISTS: 'SET_WHITELISTS',
};

export const whitelistInitState = {
  item: null,
  items: [],
  hasNext: false,
  hasPrevious: false,
  page: null,
  pages: null,
  perPage: null,
  total: 0,
  previousPage: null,
  nextPage: null,
};

export const state = () => ({
  whitelistState: { ...whitelistInitState },
});

export const getters = {
  lists(state) {
    return state.whitelistState;
  },
};

export const actions = {
  setWhiteLists({ commit }, payload) {
    commit(MUTATION_TYPE.SET_WHITELISTS, payload);
  },
};

export const mutations = {
  [MUTATION_TYPE.SET_WHITELISTS](state, payload) {
    state.whitelistState = {
      ...state.whitelistState,
      ...payload,
    };
  }
};

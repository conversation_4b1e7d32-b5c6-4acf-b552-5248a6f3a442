const MUTATION_TYPE = {
  SET_CLAIMS: 'SET_CLAIMS',
  SET_DETAIL_CLAIM: 'SET_DETAIL_CLAIM',
};

export const claimInitState = {
  item: null,
  items: [],
  hasNext: false,
  hasPrevious: false,
  page: null,
  pages: null,
  perPage: null,
  total: 0,
  previousPage: null,
  nextPage: null,
};

export const state = () => ({
  claimState: { ...claimInitState },
});

export const getters = {
  lists(state) {
    return state.claimState;
  },
  detail(state) {
    return state.claimState.item;
  },
};

export const actions = {
  setClaims({ commit }, payload) {
    commit(MUTATION_TYPE.SET_CLAIMS, payload);
  },
  setDetailClaim({ commit }, payload) {
    commit(MUTATION_TYPE.SET_DETAIL_CLAIM, payload);
  },
};

export const mutations = {
  [MUTATION_TYPE.SET_CLAIMS](state, payload) {
    state.claimState = {
      ...state.claimState,
      ...payload,
    };
  },

  [MUTATION_TYPE.SET_DETAIL_CLAIM](state, payload) {
    state.claimState = {
      ...state.claimState,
      item: payload,
    };
  },
};

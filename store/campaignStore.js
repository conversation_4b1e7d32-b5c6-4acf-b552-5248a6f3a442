const MUTATION_TYPE = {
  SET_CAMPAIGNS: 'SET_CAMPAIGNS',
  SET_CURRENT_CAMPAIGN: 'SET_CURRENT_CAMPAIGN',
};


export const state = () => {
  return ({
    currentCampaign: {
      id: 0,
      title: '<PERSON>ọn campaign',
    },
    campaigns: []
  })
};

export const getters = {
  currentCampaign(state) {
    if (state.currentCampaign.id === 0 && typeof window !== 'undefined' && localStorage.getItem(MUTATION_TYPE.SET_CURRENT_CAMPAIGN)) {
      return JSON.parse(localStorage.getItem(MUTATION_TYPE.SET_CURRENT_CAMPAIGN))
    }
    return state.currentCampaign;
  },
  campaigns(state) {
    return state.campaigns;
  },
};

export const actions = {
  setCurrentCampaign({commit}, payload) {
    commit(MUTATION_TYPE.SET_CURRENT_CAMPAIGN, payload);
  },
  setCampaigns({commit}, payload) {
    commit(MUTATION_TYPE.SET_CAMPAIGNS, payload);
  },
};

export const mutations = {
  [MUTATION_TYPE.SET_CAMPAIGNS](state, payload) {
    state.campaigns = payload;
  },

  [MUTATION_TYPE.SET_CURRENT_CAMPAIGN](state, payload) {
    state.currentCampaign = payload;
    localStorage.setItem(MUTATION_TYPE.SET_CURRENT_CAMPAIGN, JSON.stringify(state.currentCampaign));
  },
};

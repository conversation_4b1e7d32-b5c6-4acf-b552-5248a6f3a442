const MUTATION_TYPE = {
  SET_IMAGES: 'SET_IMAGES',
};

export const imageInitState = {
  item: null,
  items: [],
  hasNext: false,
  hasPrevious: false,
  page: null,
  pages: null,
  perPage: null,
  total: 0,
  previousPage: null,
  nextPage: null,
};

export const state = () => ({
  imageState: { ...imageInitState },
});

export const getters = {
  lists(state) {
    return state.imageState;
  }
};

export const actions = {
  setImages({ commit }, payload) {
    commit(MUTATION_TYPE.SET_IMAGES, payload);
  }
};

export const mutations = {
  [MUTATION_TYPE.SET_IMAGES](state, payload) {
    state.imageState = {
      ...state.imageState,
      ...payload,
    };
  },
};

const MUTATION_TYPE = {
  SET_GIFT_LIST: 'SET_GIFT_LIST',
  SET_GIFTS: 'SET_GIFTS',
  SET_DETAIL_GIFT: 'SET_DETAIL_GIFT',
};

export const giftInitState = {
  item: null,
  items: [],
  hasNext: false,
  hasPrevious: false,
  page: null,
  pages: null,
  perPage: null,
  total: 0,
  previousPage: null,
  nextPage: null,
};

export const state = () => ({
  giftState: {
    item: {},
    items: [],
    pagination: {
      page: 1,
      total: 0,
    },
  },
  giftListState: { ...giftInitState },
});

export const getters = {
  list(state) {
    return state.giftState.items;
  },
  giftList(state) {
    return state.giftListState;
  },
  giftDetail(state) {
    return state.giftListState.item;
  },
};

export const actions = {
  setGifts({ commit }, payload) {
    commit(MUTATION_TYPE.SET_GIFT_LIST, payload);
  },
  setGiftList({ commit }, payload) {
    commit(MUTATION_TYPE.SET_GIFTS, payload);
  },
  setDetailGift({ commit }, payload) {
    commit(MUTATION_TYPE.SET_DETAIL_GIFT, payload);
  },
};

export const mutations = {
  [MUTATION_TYPE.SET_GIFT_LIST](state, { data }) {
    state.giftState.items = data;
  },
  [MUTATION_TYPE.SET_GIFTS](state, payload) {
    state.giftListState = {
      ...state.giftListState,
      ...payload,
    };
  },
  [MUTATION_TYPE.SET_DETAIL_GIFT](state, payload) {
    state.giftListState = {
      ...state.giftListState,
      item: payload,
    };
  },
};

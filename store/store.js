const MUTATION_TYPE = {
  SET_STORE_LIST: 'SET_STORE_LIST',
};

export const state = () => ({
  storeState: {
    items: [],
    page: 1,
    total: 0,
  },
});

export const getters = {
  items(state) {
    return state.storeState.items;
  },
};

export const actions = {
  setStores({ commit }, payload) {
    commit(MUTATION_TYPE.SET_STORE_LIST, payload);
  },
};

export const mutations = {
  [MUTATION_TYPE.SET_STORE_LIST](state, data) {
    state.storeState = {
      ...state.storeState,
      ...data,
    };
  },
};

const MUTATION_TYPE = {
  SET_LOADING: 'SET_LOADING',
  SET_USER_DATA: 'SET_USER_DATA',
  SET_USER_LOADED: 'SET_USER_LOADED',
  CLEAR_USER_DATA: 'CLEAR_USER_DATA',
  SET_USER_LOADING: 'SET_USER_LOADING',
};

export const state = () => ({
  isLoading: false,
  // Only user data storage - no permission caching
  userData: null,
  userLoaded: false,
  userLoading: false, // Thêm flag để tránh gọi API trùng lặp
});

export const getters = {
  isAuthenticated(state) {
    return state.auth.loggedIn;
  },

  loggedInUser(state) {
    return state.auth.user;
  },

  getIsLoading(state) {
    return state.isLoading;
  },

  // Only user data getters
  getUserData(state) {
    return state.userData;
  },
  isUserLoaded(state) {
    return state.userLoaded;
  },
  isUserLoading(state) {
    return state.userLoading;
  },
};

export const actions = {
  async nuxtServerInit({ commit, state }, { store, $axios }) {

  },
  setLoading({ commit }, payload) {
    commit(MUTATION_TYPE.SET_LOADING, payload);
  },

  // Simple user data management - no permission caching
  async loadUserData({ commit, state }, { $axios, $auth }) {
    try {


      commit(MUTATION_TYPE.SET_USER_LOADING, true);
      commit(MUTATION_TYPE.SET_LOADING, true);

      // Kiểm tra xem user đã đăng nhập chưa
      if (!$auth.loggedIn) {
        console.warn('Auth not ready or user not logged in:', {
          loggedIn: $auth.loggedIn,
          token: $auth.getToken('local')
        });
        throw new Error('User not logged in');
      }

      console.log('Calling API /user/me...');
      // Call API to get user data
      const { data } = await $axios.get('user/me');

      if (!data) {
        throw new Error('No user data received from API');
      }

      console.log('API response received:', data);

      // Set user in auth
      await $auth.setUser(data);

      // Store user data in Vuex (simple)
      commit(MUTATION_TYPE.SET_USER_DATA, data);
      commit(MUTATION_TYPE.SET_USER_LOADED, true);

      commit(MUTATION_TYPE.SET_LOADING, false);
      commit(MUTATION_TYPE.SET_USER_LOADING, false);
      return data;
    } catch (error) {
      console.error('Error loading user data:', error);
      commit(MUTATION_TYPE.SET_LOADING, false);
      commit(MUTATION_TYPE.SET_USER_LOADING, false);

      // Clear user data on error
      commit(MUTATION_TYPE.CLEAR_USER_DATA);

      // Nếu lỗi 401, logout user
      if (error?.response?.status === 401) {
        console.warn('Got 401, logging out...');
        await $auth.logout();
      }

      throw error;
    }
  },

  clearUserData({ commit }) {
    commit(MUTATION_TYPE.CLEAR_USER_DATA);
  },

  setUserData({ commit }, userData) {
    commit(MUTATION_TYPE.SET_USER_DATA, userData);
    commit(MUTATION_TYPE.SET_USER_LOADED, true);
  },
};

export const mutations = {
  [MUTATION_TYPE.SET_LOADING](state, payload) {
    state.isLoading = payload;
  },

  // Only user data mutations
  [MUTATION_TYPE.SET_USER_DATA](state, userData) {
    state.userData = userData;
  },
  [MUTATION_TYPE.SET_USER_LOADED](state, loaded) {
    state.userLoaded = loaded;
  },
  [MUTATION_TYPE.SET_USER_LOADING](state, loading) {
    state.userLoading = loading;
  },
  [MUTATION_TYPE.CLEAR_USER_DATA](state) {
    state.userData = null;
    state.userLoaded = false;
    state.userLoading = false;
  },
};

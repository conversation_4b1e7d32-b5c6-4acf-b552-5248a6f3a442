import _ from 'lodash';
import moment from 'moment';
import { CLAIM_PROCESS_LIST } from '@/util/constant.js';

export const camelCaseKeys = (obj) => {
  if (!_.isObject(obj)) {
    return obj;
  } else if (_.isArray(obj)) {
    return obj.map((v) => camelCaseKeys(v));
  }
  return _.reduce(
    obj,
    (r, v, k) => {
      return {
        ...r,
        [_.camelCase(k)]: camelCaseKeys(v),
      };
    },
    {},
  );
};

export const snakeCaseKeys = (obj) => {
  if (!_.isObject(obj)) {
    return obj;
  } else if (_.isArray(obj)) {
    return obj.map((v) => snakeCaseKeys(v));
  }
  return _.reduce(
    obj,
    (r, v, k) => {
      return {
        ...r,
        [_.snakeCase(k)]: snakeCaseKeys(v),
      };
    },
    {},
  );
};

export const convertToVietnamesePhone = (value) => {
  const prefix = '84';
  if (!value) {
    return '';
  }

  value = value.toString();

  if (value.startsWith('0')) {
    value = value.replace('0', prefix);
  } else if (value.startsWith('84')) {
    value = value.replace('84', prefix);
  } else {
    value = prefix + value;
  }

  return value;
};

export function getApiErrorMessage(code) {
  let message;
  switch (code) {
    case 404010:
      message = 'Model không nằm trong danh sách tham gia chương trình ';
      break;
    case 404020:
      message = 'Không tìm thấy địa chỉ cửa hàng';
      break;
    case 400031:
      message = 'Serial bị trùng. Vui lòng upload lại ảnh khác';
      break;
    case 400040:
      message = 'Serial này đã được sử dụng. Vui lòng upload lại ảnh khác';
      break;
    case 400042:
      message = 'Model hoặc Serial không tồn tại. Vui lòng upload lại ảnh khác';
      break;
    case 400043:
      message = 'Không tìm thấy serial. Vui lòng upload lại ảnh khác';
      break;
    case 400050:
      message = 'Dung lượng ảnh quá lớn. Vui lòng upload lại ảnh khác';
      break;
    case 500050:
      message = 'Tải ảnh thất bại. Vui lòng thử lại.';
      break;
    case 400060:
      message = 'Không tìm thấy model. Vui lòng upload lại ảnh khác';
      break;
    default:
      message = 'Quá thời gian chờ tải. Vui lòng thử lại';
      break;
  }
  return message;
}

export const convertExportClaimData = (data) => {
  return data?.map((item) => {
    const note = item.note;
    let notes = '';
    let noteCall1 = '';
    let noteCall2 = '';
    let noteCall3 = '';
    let noteUyQuyen = '';

    if (note?.length > 0) {
      note.forEach(_item => {
        if (parseInt(_item.type) === 0) {
          notes += _item.note + '\n';
        }
        if (parseInt(_item.type) === 4) {
          noteUyQuyen += _item.note + '\n';
        }
        if (parseInt(_item.type) === 1) {
          noteCall1 += _item.note + '\n';
        }
        if (parseInt(_item.type) === 2) {
          noteCall2 += _item.note + '\n';
        }
        if (parseInt(_item.type) === 3) {
          noteCall3 += _item.note + '\n';
        }
      });
    }
    const warranty_data = item.warranty_data ?? null;
    let warranty = '';

    if (warranty_data) {
      warranty += warranty_data.message ? warranty_data.message + '\n' : '';
      warranty += 'Model: ' + (warranty_data.model_name ?? 'N/A') + '\n';
      warranty += 'Serial: ' + (warranty_data.serial_number ?? 'N/A') + '\n';
      warranty += 'Tên sản phẩm: ' + (warranty_data.product ?? 'N/A') + '\n';
      warranty += 'Ngày kích hoạt bảo hành: ' + (warranty_data.purchase_date ?? 'N/A') + '\n';
      warranty += 'Nơi kích hoạt bảo hành: ' + (warranty_data.purchase_location ?? 'N/A') + '\n';
      warranty += 'Ngày hết bảo hành: ' + (warranty_data.warranty_period_date ?? 'N/A');
    }


    return {
      ...item,
      note: notes,
      noteCall1: noteCall1,
      noteCall2: noteCall2,
      noteCall3: noteCall3,
      noteUyQuyen: noteUyQuyen,
      order_date: moment(item.order_date * 1000).format('DD/MM/YYYY'),
      created_date: moment(item.created_date * 1000).format('DD/MM/YYYY HH:mm:ss'),
      approve_status: CLAIM_PROCESS_LIST.find((process) => process.VALUE === item.approve_status).TEXT,
      gift_type: item.gift_type,
      email: item.email || '',
      active_kplus: item.acttive_k_plus === undefined ? '' : item.acttive_k_plus === 2 ? 'Có' : 'Không',
      delivery_address:
        item.delivery_info && item.delivery_info.ward_id !== undefined
          ? item.delivery_info?.address + ', ' + (item.delivery_info?.ward_id || '')
          : '',
      delivery_name: item.delivery_info && item.delivery_info.ward_id !== undefined ? item.delivery_info?.name : '',
      delivery_phone: item.delivery_info && item.delivery_info.ward_id !== undefined ? item.delivery_info?.phone : '',
      warranty_data: warranty,
    };
  });
};

export const convertClaimInfo = (data) => {
  return data;
};

export const convertImage = (data) => {
  return data.map((item) => {
    return {
      ...item,
      created: moment(item.created * 1000).format('DD/MM/YYYY HH:mm:ss'),
    };
  });
};
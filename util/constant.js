export const CAMPAIGN = {
  PHASE_1: {
    VALUE: 1,
    LABEL: 'Phase 1',
  },
  PHASE_2: {
    VALUE: 3,
    LABEL: 'Phase 2',
  },
};

export const CAMPAIGN_LIST = Object.keys(CAMPAIGN).map((key) => CAMPAIGN[key]);

export const APPROVE_STATUS = {
  APPROVAL: {
    VALUE: 2,
    TEXT: 'Duyệt',
  },
  DENY: {
    VALUE: 1,
    TEXT: 'Từ chối',
  },
};

export const APPROVE_STATUS_LIST = Object.keys(APPROVE_STATUS).map((key) => APPROVE_STATUS[key]);

export const CLAIM_PROCESS = {
  PENDING: {
    VALUE: 1,
    TEXT: 'Chờ xử lý',
  },
  APPROVED_1: {
    VALUE: 2,
    TEXT: 'Duyệt lần 1',
  },
  APPROVED_2: {
    VALUE: 3,
    TEXT: 'Duyệt lần 2',
  },
  DENIED_1: {
    VALUE: 4,
    TEXT: 'Từ chối lần 1',
  },
  DENIED_2: {
    VALUE: 5,
    TEXT: 'Từ chối lần 2',
  },
  IN_PROCESS: {
    VALUE: 6,
    TEXT: 'Đang xử lý',
  },
};

export const CLAIM_PROCESS_LIST = Object.keys(CLAIM_PROCESS).map((key) => CLAIM_PROCESS[key]);

export const CLAIM_TEMP_PROCESS = {
  PENDING: {
    VALUE: 1,
    TEXT: 'Chờ quét',
  },
  SUCCESS: {
    VALUE: 2,
    TEXT: 'Hợp lệ',
  },
  IN_PROCESS: {
    VALUE: 3,
    TEXT: 'Đã quét',
  },
}
export const CLAIM_TEMP_PROCESS_LIST = Object.keys(CLAIM_TEMP_PROCESS).map((key) => CLAIM_TEMP_PROCESS[key]);
export const CLAIM_DETAIL_PROCESS = {
  PENDING: {
    VALUE: 1,
    TEXT: 'Chờ xử lý',
  },
  DATA_CORRUPT: {
    VALUE: 2,
    TEXT: 'Lỗi dữ liệu',
  },
  CANNOT_GET_GIFT: {
    VALUE: 3,
    TEXT: 'Không lấy được quà tặng',
  },
  CANNOT_SEND_SMS: {
    VALUE: 4,
    TEXT: 'Không thể gửi tin nhắn',
  },
  SUCCESS: {
    VALUE: 5,
    TEXT: 'Gửi quà thành công',
  },
};

export const URBOX_STATUS = {
  ACTIVE: {
    VALUE: 2,
    TEXT: 'Hoạt động',
  },
  DEACTIVE: {
    VALUE: 1,
    TEXT: 'Đóng',
  },
};

export const EXPORT_CLAIM_COLUMN = [
  {
    label: 'ID',
    field: 'id',
  },
  {
    label: 'Ngày tạo đơn',
    field: 'created_date',
  },
  {
    label: 'Ngày mua hàng',
    field: 'order_date',
  },
  {
    label: 'Họ tên',
    field: 'fullname',
  },
  {
    label: 'Số điện thoại',
    field: 'phone',
  },
  {
    label: 'Email',
    field: 'email',
  },
  {
    label: 'Serial',
    field: 'serial',
  },
  {
    label: 'Model',
    field: 'model',
  },
  {
    label: 'Loại quà',
    field: 'gift_type',
  },
  {
    label: 'Trạng thái duyệt quà',
    field: 'approve_status',
  },
  {
    label: 'Tài khoản duyệt lần 1',
    field: 'approve_one_account',
  },
  {
    label: 'Tài khoản duyệt lần 2',
    field: 'approve_two_account',
  },
  {
    label: 'Ghi chú duyệt lần 1',
    field: 'approve_one_note',
  },
  {
    label: 'Ghi chú duyệt lần 2',
    field: 'approve_two_note',
  },
  {
    label: 'Đăng kí K+',
    field: 'active_kplus',
  },
  {
    label: 'Người nhận quà vật lý',
    field: 'delivery_name',
  },
  {
    label: 'Số điện thoại người nhận',
    field: 'delivery_phone',
  },
  {
    label: 'Địa chỉ giao quà vật lý',
    field: 'delivery_address',
  },
  {
    label: 'Ghi chú',
    field: 'note',
  },
  {
    label: 'GL1',
    field: 'noteCall1',
  },
  {
    label: 'GL2',
    field: 'noteCall2',
  },
  {
    label: 'GL3',
    field: 'noteCall3',
  },
  {
    label: 'SĐT uỷ quyền',
    field: 'noteUyQuyen',
  },
  {
    label: 'Bill Image',
    field: 'bill_image',
  },
  {
    label: 'Model Image',
    field: 'model_image',
  },
  {
    label: 'Thông tin bảo hành',
    field: 'warranty_data',
  },
];
export const EXPORT_IMAGES_COLUMN = [
  {
    label: 'Tên ảnh',
    field: 'filename',
  },
  {
    label: 'Url',
    field: 'url',
  },
  {
    label: 'Ngày upload',
    field: 'created',
  }
]
export const OCR_DROPDOWN_OPTIONS = {
  OCR_CORRECT_UNMATCHED: {
    VALUE: 1,
    TEXT: 'OCR đúng, so sánh sai',
  },
  OCR_INCORRECT: {
    VALUE: 2,
    TEXT: 'OCR sai',
  },
  OCR_CORRECT_MATCHED: {
    VALUE: 3,
    TEXT: 'OCR đúng, so sánh đúng',
  },
  OTHER: {
    VALUE: 4,
    TEXT: 'Khác',
  },
};

export const OCR_DROPDOWN_OPTIONS_LIST = (() => {
  let list = Object.keys(OCR_DROPDOWN_OPTIONS).map((key) => OCR_DROPDOWN_OPTIONS[key]);
  let itemToMove = list.splice(2, 1)[0];
  list.unshift(itemToMove);
  return list
})();

import * as permissions from '~/util/permissions'

export default function ({ $auth, store }, inject) {
    // Helper function to get user data from store first, fallback to $auth.user
    const getUserData = () => {
        // Try store first (more reliable)
        const storeUserData = store.getters.getUserData;
        if (storeUserData) {
            return storeUserData;
        }
        // Fallback to $auth.user
        return $auth?.user;
    };

    // Create a permissions object that performs real-time checks
    const $permissions = {
        // Core permission checks - always real-time
        hasPermission: (permissionName) => {
            const userData = getUserData();
            if (!userData) {
                console.warn('User data not available for permission check:', permissionName);
                return false;
            }
            return permissions.hasPermission(userData, permissionName);
        },

        canPerformAction: (resource, action) => {
            const userData = getUserData();
            if (!userData) {
                console.warn('User data not available for action check:', resource, action);
                return false;
            }
            return permissions.hasPermissionByResourceAction(userData, resource, action);
        },

        // Utility functions
        getUserPermissions: () => {
            const userData = getUserData();
            return permissions.getUserPermissions(userData);
        },

        getUserData: () => {
            return getUserData();
        },

        // Check if user is loaded from store
        isUserLoaded: () => {
            return store.getters.isUserLoaded;
        },

        // Specific permission checks - all real-time from user data
        canCreateClaim: () => {
            const userData = getUserData();
            return userData ? permissions.canCreateClaim(userData) : false;
        },

        canManageGifts: () => {
            const userData = getUserData();
            return userData ? permissions.canManageGifts(userData) : false;
        },

        canManageCampaigns: () => {
            const userData = getUserData();
            return userData ? permissions.canManageCampaigns(userData) : false;
        },

        canViewClaim: () => {
            const userData = getUserData();
            return userData ? permissions.canViewClaim(userData) : false;
        },

        canEditClaim: () => {
            const userData = getUserData();
            return userData ? permissions.canEditClaim(userData) : false;
        },

        canDeleteClaim: () => {
            const userData = getUserData();
            return userData ? permissions.canDeleteClaim(userData) : false;
        },

        canChangeOcr: () => {
            const userData = getUserData();
            return userData ? permissions.canChangeOcr(userData) : false;
        },

        canUpdateOrder: () => {
            const userData = getUserData();
            return userData ? permissions.canUpdateOrder(userData) : false;
        },

        cantUpdateOrder: () => {
            const userData = getUserData();
            return userData ? permissions.cantUpdateOrder(userData) : true; // Default to cannot update if no user
        },

        // User management permissions
        canCreateUser: () => {
            const userData = getUserData();
            return userData ? permissions.canCreateUser(userData) : false;
        },

        canUpdateUser: () => {
            const userData = getUserData();
            return userData ? permissions.canUpdateUser(userData) : false;
        },

        canViewUsers: () => {
            const userData = getUserData();
            return userData ? permissions.canViewUsers(userData) : false;
        },

        canDeleteUser: () => {
            const userData = getUserData();
            return userData ? permissions.canDeleteUser(userData) : false;
        },

        canManageUsers: () => {
            const userData = getUserData();
            return userData ? permissions.canManageUsers(userData) : false;
        },

        // Advanced checks
        hasAnyPermission: (permissionNames) => {
            return permissions.hasAnyPermission(getUserData(), permissionNames);
        },

        hasAllPermissions: (permissionNames) => {
            return permissions.hasAllPermissions(getUserData(), permissionNames);
        },

        // Backward compatibility functions for old getter names
        getCanCreateClaim: () => {
            return permissions.canCreateClaim(getUserData());
        },

        getCantUpdateOrder: () => {
            return permissions.cantUpdateOrder(getUserData());
        },

        getCanChangeOcr: () => {
            return permissions.canChangeOcr(getUserData());
        },

        // Raw functions (if you need to pass custom user)
        raw: permissions
    }

    // Inject into context as $permissions
    inject('permissions', $permissions)
} 
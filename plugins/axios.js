import httpStatus from 'http-status';
import { MessageBox, Message } from 'element-ui';

let isShowMessageBox = false;

export default function ({ $axios, store, $config }) {
  $axios.onRequest((config) => {
    const currentCampaign = store.getters['campaignStore/currentCampaign'];
    if (config.url.includes('delivery_info') || config.url.includes('location') || config.url.includes('v2')) {
      config.baseURL = $config.baseURL.replace('v1', 'v2');
    } else {
      config.baseURL = $config.baseURL;
    }
    if (config.url !== '/user/login') {
      if (config.params) {
        config.params.campaign_id = currentCampaign.id || $config.campaignId;
      } else {
        config.params = {
          campaign_id: currentCampaign.id || $config.campaignId,
        };
      }

      if (config.data) {
        config.data.campaign_id = currentCampaign.id || $config.campaignId;
      }
    } else {
      config.data.campaign_id = $config.campaignId;
    }

    return config;
  });

  $axios.onError((error) => {
    if (error.response && error.response.status === httpStatus.UNAUTHORIZED) {
      if (error.response.data.error.message === 'Password is not valid') {
        return Promise.reject(error.response);
      }
      if (!isShowMessageBox) {
        isShowMessageBox = true;
        MessageBox.confirm(
          'You have been logged out, you can cancel to stay on this page, or log in again',
          'Confirm logout',
          {
            confirmButtonText: 'Re-Login',
            cancelButtonText: 'Cancel',
            type: 'warning',
          },
        )
          .then((res) => {
            console.log(res);
            isShowMessageBox = false;
            store.$auth.logout();
          })
          .catch((e) => {
            console.log(e);
          });
      }
    } else {
      Message({
        message: JSON.stringify(error?.response?.data?.error?.message || error?.response?.data?.message || error?.message || "Lỗi hệ thống"),
        type: 'error',
        duration: 5 * 1000,
      });
      return error;
    }

    return Promise.reject(error.response);
  });

  $axios.onResponse((response) => {
    return response.data;
  });
}

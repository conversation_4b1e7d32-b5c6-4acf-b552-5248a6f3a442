export default (context, inject) => {
  const formatPrice = (price) => {
    if (typeof price === 'undefined' || price === '' || price < 0) return ''
    return new Intl.NumberFormat('vi-VN').format(parseFloat(price)) + "đ";
  }
  inject('formatPrice', formatPrice)
  context.$formatPrice = formatPrice

  const get_img = (images) => {
    if (images === null || images === '' || typeof images === 'undefined' || images.length === 0) return '/img/vna-banner.png'
    if (typeof images === 'object') {
      images = Object.values(images)
    }
    let defaultImage = images.find(image => image.is_default === 2)
    if (typeof defaultImage === 'undefined' || defaultImage === '') {
      defaultImage = images[0]
    }
    return defaultImage.url || '/img/vna-banner.png'
  }
  inject('get_img', get_img)
  context.$get_img = get_img

}

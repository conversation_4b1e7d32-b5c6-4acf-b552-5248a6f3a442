import Vue from 'vue';
import moment from 'moment';

Vue.filter('formatDateFromInt', function (value) {
  if (!value) {
    return '';
  }
  return moment(parseInt(value, 0) * 1000).format('DD-MM-YYYY');
});

Vue.filter('formatDatetimeFromInt', function (value) {
  if (!value) {
    return '';
  }
  return moment(parseInt(value, 0) * 1000).format('DD-MM-YYYY HH:mm:ss');
});

Vue.filter('formatPhone', function (value) {
  let prefix = '(+84) ';
  if (!value) {
    return '';
  }

  value = value.toString();
  if (value.startsWith('0')) {
    value = value.replace('0', prefix);
  } else if (value.startsWith('84')) {
    value = value.replace('84', prefix);
  } else {
    value = prefix + value;
  }

  return value;
});

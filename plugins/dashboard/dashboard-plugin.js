import Vue from 'vue';
import '@/polyfills';
import Notifications from '~/components/argon-core/NotificationPlugin';
import VueExcelXlsx from 'vue-excel-xlsx';
import SideBar from '~/components/argon-core/SidebarPlugin';
import lang from 'element-ui/lib/locale/lang/vi';
import locale from 'element-ui/lib/locale';
import './globalDirectives';
import './globalComponents';
import { extend, configure, ValidationObserver, ValidationProvider } from 'vee-validate';
import * as rules from 'vee-validate/dist/rules';
import { messages } from 'vee-validate/dist/locale/vi.json';

messages.required = '{_field_} không được phép để trống';
Object.keys(rules).forEach((rule) => {
  extend(rule, {
    ...rules[rule], // copies rule configuration
    message: messages[rule], // assign message
  });
});

Vue.component('ValidationProvider', ValidationProvider);
Vue.component('ValidationObserver', ValidationObserver);

locale.use(lang);
Vue.use(SideBar);
Vue.use(Notifications);
Vue.use(VueExcelXlsx);

configure({
  classes: {
    valid: 'is-valid',
    invalid: 'is-invalid',
    dirty: ['is-dirty', 'is-dirty'], // multiple classes per flag!
  },
});
